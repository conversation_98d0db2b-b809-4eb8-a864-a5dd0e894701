# Import necessary libraries
import os
import re
import random
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
from tqdm.notebook import tqdm
import warnings
warnings.filterwarnings('ignore')

# Import PyTorch components
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, Subset
from torchvision import transforms, models

# Import scikit-learn for splitting and metrics
from sklearn.model_selection import train_test_split
from sklearn.metrics import confusion_matrix, classification_report
import seaborn as sns

# Set random seeds for reproducibility
random.seed(42)
np.random.seed(42)
torch.manual_seed(42)
if torch.cuda.is_available():
    torch.cuda.manual_seed_all(42)
    torch.backends.cudnn.benchmark = True  # Speed up training

# Device configuration
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# Define Dataset Path
DATASET_PATH = "C:/Users/<USER>/OneDrive/Desktop/Data Science/New Example/CT Scan/CT-KIDNEY-DATASET-Normal-Cyst-Tumor-Stone/Resized"

# Get class labels (folder names)
class_labels = sorted([d for d in os.listdir(DATASET_PATH) if os.path.isdir(os.path.join(DATASET_PATH, d))])

# Print basic info
print(f"Found {len(class_labels)} classes: {class_labels}")

# Dictionary to store image file paths
image_files = {label: [] for label in class_labels}

# Read images from each class
for label in class_labels:
    class_dir = os.path.join(DATASET_PATH, label)
    image_files[label] = [os.path.join(class_dir, img) for img in os.listdir(class_dir) if img.endswith(('.png', '.jpg', '.jpeg'))]

# Print number of images per class
total_images = 0
for label, files in image_files.items():
    print(f"Class '{label}': {len(files)} images")
    total_images += len(files)
    
print(f"\nTotal images in dataset: {total_images}")

# Function to extract patient ID from filename
def get_patient_id(filename):
    # Extract patient ID from filename using regex
    match = re.search(r'(\d+)', os.path.basename(filename))
    return match.group(1) if match else os.path.basename(filename)

# Custom dataset class with patient ID tracking
class KidneyCTDataset(Dataset):
    def __init__(self, root_dir, transform=None):
        self.root_dir = root_dir
        self.transform = transform
        self.classes = sorted([d for d in os.listdir(root_dir) if os.path.isdir(os.path.join(root_dir, d))])
        self.class_to_idx = {cls: i for i, cls in enumerate(self.classes)}
        
        self.samples = []
        self.patient_to_samples = {}
        
        # Load all image paths and organize by patient ID
        for class_name in self.classes:
            class_dir = os.path.join(root_dir, class_name)
            for img_name in os.listdir(class_dir):
                if img_name.endswith(('.png', '.jpg', '.jpeg')):
                    img_path = os.path.join(class_dir, img_name)
                    patient_id = get_patient_id(img_path)
                    
                    # Add to samples list
                    idx = len(self.samples)
                    self.samples.append((img_path, self.class_to_idx[class_name]))
                    
                    # Add to patient mapping
                    if patient_id not in self.patient_to_samples:
                        self.patient_to_samples[patient_id] = []
                    self.patient_to_samples[patient_id].append(idx)
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        img_path, label = self.samples[idx]
        
        # Load image
        image = Image.open(img_path).convert('RGB')
        
        # Apply transformations
        if self.transform:
            image = self.transform(image)
        
        return image, label

# Define transformations
transform = transforms.Compose([
    transforms.Resize((224, 224)),  # RegNetY-8GF also uses 224x224 input
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

# Load dataset
dataset = KidneyCTDataset(root_dir=DATASET_PATH, transform=transform)

# Get all patient IDs
patient_ids = list(dataset.patient_to_samples.keys())
print(f"Total patients: {len(patient_ids)}")
print(f"Total images: {len(dataset)}")

# Step 1: Perform a clean 80-20 split by patient ID
train_patients, test_patients = train_test_split(
    patient_ids, 
    test_size=0.2, 
    random_state=42
)

print(f"Train-Test Split (by patient):")
print(f"  Train: {len(train_patients)} patients ({len(train_patients)/len(patient_ids):.1%} of all patients)")
print(f"  Test: {len(test_patients)} patients ({len(test_patients)/len(patient_ids):.1%} of all patients)")

# Get sample indices for each split
train_indices = [idx for patient in train_patients for idx in dataset.patient_to_samples[patient]]
test_indices = [idx for patient in test_patients for idx in dataset.patient_to_samples[patient]]

print(f"\nTrain-Test Split (by image):")
print(f"  Train: {len(train_indices)} images ({len(train_indices)/len(dataset):.1%} of all images)")
print(f"  Test: {len(test_indices)} images ({len(test_indices)/len(dataset):.1%} of all images)")

# Step 2: Divide the training patients into 5 completely separate folds
# Shuffle the training patients
random.shuffle(train_patients)

# Split into 5 equal groups
num_folds = 5
fold_size = len(train_patients) // num_folds
fold_patients = [train_patients[i*fold_size:(i+1)*fold_size] for i in range(num_folds)]

# Handle any remaining patients
for i in range(len(train_patients) % num_folds):
    fold_patients[i].append(train_patients[num_folds*fold_size + i])

# Create a list to store fold information
folds = []

print("\n5-Fold Split (No Data Reuse):")
for fold_idx in range(num_folds):
    # Get sample indices for this fold
    fold_indices = [idx for patient in fold_patients[fold_idx] for idx in dataset.patient_to_samples[patient]]
    
    # Store fold information
    folds.append({
        'patients': fold_patients[fold_idx],
        'indices': fold_indices
    })
    
    print(f"Fold {fold_idx+1}: {len(fold_patients[fold_idx])} patients, {len(fold_indices)} images")

# Verify no overlap between folds
for i in range(num_folds):
    for j in range(i+1, num_folds):
        overlap = set(folds[i]['indices']).intersection(set(folds[j]['indices']))
        if len(overlap) > 0:
            print(f"❌ Overlap between fold {i+1} and fold {j+1}: {len(overlap)} images")
        else:
            print(f"✓ No overlap between fold {i+1} and fold {j+1}")

# Create test dataset
test_dataset = Subset(dataset, test_indices)

# Create test data loader (same for all folds)
test_loader = DataLoader(
    test_dataset, 
    batch_size=8, 
    shuffle=False, 
    num_workers=0,
    pin_memory=True
)

# Create fold datasets
fold_datasets = [Subset(dataset, fold['indices']) for fold in folds]

# Print fold sizes
print("Fold dataset sizes:")
for i, fold_dataset in enumerate(fold_datasets):
    print(f"  Fold {i+1}: {len(fold_dataset)} images")
print(f"  Test: {len(test_dataset)} images")

# Function to train a model for one epoch
def train_epoch(model, train_loader, criterion, optimizer, epoch):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    
    # Use tqdm for progress bar
    with tqdm(train_loader, desc=f'Epoch {epoch+1}', leave=False) as t:
        for inputs, labels in t:
            inputs, labels = inputs.to(device), labels.to(device)
            
            # Zero the parameter gradients
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            
            # Backward pass and optimize
            loss.backward()
            optimizer.step()
            
            # Track statistics
            running_loss += loss.item() * inputs.size(0)
            _, predicted = torch.max(outputs, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            
            # Update progress bar
            t.set_postfix(loss=loss.item(), acc=100.*correct/total)
    
    epoch_loss = running_loss / len(train_loader.dataset)
    epoch_acc = 100. * correct / total
    
    return epoch_loss, epoch_acc

# Function to evaluate the model
def evaluate(model, data_loader, criterion):
    model.eval()
    running_loss = 0.0
    correct = 0
    total = 0
    
    with torch.no_grad():
        for inputs, labels in data_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            
            # Track statistics
            running_loss += loss.item() * inputs.size(0)
            _, predicted = torch.max(outputs, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
    
    epoch_loss = running_loss / len(data_loader.dataset)
    epoch_acc = 100. * correct / total
    
    return epoch_loss, epoch_acc

# Function to generate predictions and true labels
def get_predictions(model, data_loader, device):
    model.eval()
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for inputs, labels in data_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            
            # Forward pass
            outputs = model(inputs)
            _, predicted = torch.max(outputs, 1)
            
            # Collect predictions and labels
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    return np.array(all_preds), np.array(all_labels)

# Function to plot confusion matrix
def plot_confusion_matrix(cm, classes, normalize=False, title='Confusion Matrix', cmap=plt.cm.Blues):
    plt.figure(figsize=(10, 8))
    
    if normalize:
        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        fmt = '.2f'
        title = 'Normalized ' + title
    else:
        fmt = 'd'
    
    # Create heatmap
    sns.heatmap(cm, annot=True, fmt=fmt, cmap=cmap, 
                xticklabels=classes, yticklabels=classes, 
                square=True, cbar=True)
    
    plt.title(title, fontsize=16)
    plt.ylabel('True Label', fontsize=12)
    plt.xlabel('Predicted Label', fontsize=12)
    plt.tight_layout()
    plt.show()

# Function to initialize RegNetY-8GF model
def initialize_regnet_y_8gf():
    # Load pre-trained RegNetY-8GF model
    model = models.regnet_y_8gf(weights='DEFAULT')
    
    # Modify the final fully connected layer for our number of classes
    num_ftrs = model.fc.in_features
    model.fc = nn.Linear(num_ftrs, len(class_labels))
    
    return model.to(device)

# Function to train RegNetY-8GF models on each fold
def train_regnet_y_8gf_on_folds(num_epochs=10, batch_size=8):
    # Results storage
    fold_results = []
    all_train_accs = []
    all_test_accs = []
    
    # Loop through each fold
    for fold_idx in range(num_folds):
        print(f"\n=== Training RegNetY-8GF on Fold {fold_idx+1}/{num_folds} ===\n")
        
        # Create data loader for this fold
        train_loader = DataLoader(
            fold_datasets[fold_idx],
            batch_size=batch_size,
            shuffle=True,
            num_workers=0,
            pin_memory=True
        )
        
        print(f"Training on {len(fold_datasets[fold_idx])} images")
        
        # Initialize RegNetY-8GF model
        model = initialize_regnet_y_8gf()
        
        # Loss function and optimizer
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        
        # Training loop
        results = {
            'train_loss': [], 'train_acc': [],
            'test_loss': 0, 'test_acc': 0,
            'final_train_acc': 0
        }
        
        for epoch in range(num_epochs):
            # Train for one epoch
            train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, epoch)
            
            # Print statistics
            print(f'Epoch {epoch+1}/{num_epochs}:')
            print(f'  Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%')
            
            # Save statistics
            results['train_loss'].append(train_loss)
            results['train_acc'].append(train_acc)
            
            # Save the final train accuracy for the last epoch
            if epoch == num_epochs - 1:
                results['final_train_acc'] = train_acc
                all_train_accs.append(train_acc)
        
        # Save the final model
        model_path = f'regnet_y_8gf_fold{fold_idx+1}.pth'
        torch.save(model.state_dict(), model_path)
        print(f'Saved final model to {model_path}')
        
        # Evaluate on test set after training is complete
        test_loss, test_acc = evaluate(model, test_loader, criterion)
        results['test_loss'] = test_loss
        results['test_acc'] = test_acc
        all_test_accs.append(test_acc)
        
        print(f'\nFold {fold_idx+1} Results:')
        print(f'  Final Train Acc: {results["final_train_acc"]:.2f}%')
        print(f'  Test Loss: {test_loss:.4f}, Test Acc: {test_acc:.2f}%')
        
        # Generate confusion matrix for this fold
        print(f"\nGenerating confusion matrix for Fold {fold_idx+1}...")
        y_pred, y_true = get_predictions(model, test_loader, device)
        cm = confusion_matrix(y_true, y_pred)
        
        print(f"Confusion Matrix for Fold {fold_idx+1}:")
        print(cm)
        
        # Generate classification report
        report = classification_report(y_true, y_pred, target_names=class_labels)
        print(f"\nClassification Report for Fold {fold_idx+1}:")
        print(report)
        
        # Plot confusion matrices
        plot_confusion_matrix(cm, class_labels, normalize=False, 
                             title=f'RegNetY-8GF Fold {fold_idx+1} Confusion Matrix')
        plot_confusion_matrix(cm, class_labels, normalize=True, 
                             title=f'RegNetY-8GF Fold {fold_idx+1} Normalized Confusion Matrix')
        
        # Add to fold results
        fold_results.append(results)
    
    # Calculate average results across all folds
    avg_train_acc = np.mean(all_train_accs)
    avg_test_acc = np.mean(all_test_accs)
    
    print('\n=== Overall RegNetY-8GF Results ===')
    print(f'Average Train Accuracy: {avg_train_acc:.2f}%')
    print(f'Average Test Accuracy: {avg_test_acc:.2f}%')
    
    # Plot training accuracy
    plt.figure(figsize=(15, 5))
    for i, fold_result in enumerate(fold_results):
        plt.plot(fold_result['train_acc'], '-', label=f'Fold {i+1} Train')
    plt.title(f'RegNetY-8GF - Training Accuracy', fontsize=14)
    plt.xlabel('Epoch', fontsize=12)
    plt.ylabel('Accuracy (%)', fontsize=12)
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.show()
    
    # Display final test accuracies
    plt.figure(figsize=(10, 6))
    test_accs = [fold['test_acc'] for fold in fold_results]
    plt.bar(range(1, num_folds+1), test_accs, color='#3498db')
    plt.axhline(y=avg_test_acc, color='r', linestyle='-', label=f'Average: {avg_test_acc:.2f}%')
    plt.title(f'RegNetY-8GF - Test Accuracy by Fold', fontsize=14)
    plt.xlabel('Fold', fontsize=12)
    plt.ylabel('Test Accuracy (%)', fontsize=12)
    plt.xticks(range(1, num_folds+1))
    plt.legend()
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # Add value labels
    for i, v in enumerate(test_accs):
        plt.text(i+1, v+1, f"{v:.2f}%", ha='center', fontsize=10)
        
    plt.tight_layout()
    plt.show()
    
    # Display final train accuracies
    plt.figure(figsize=(10, 6))
    train_accs = [fold['final_train_acc'] for fold in fold_results]
    plt.bar(range(1, num_folds+1), train_accs, color='#2ecc71')
    plt.axhline(y=avg_train_acc, color='r', linestyle='-', label=f'Average: {avg_train_acc:.2f}%')
    plt.title(f'RegNetY-8GF - Final Train Accuracy by Fold', fontsize=14)
    plt.xlabel('Fold', fontsize=12)
    plt.ylabel('Train Accuracy (%)', fontsize=12)
    plt.xticks(range(1, num_folds+1))
    plt.legend()
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # Add value labels
    for i, v in enumerate(train_accs):
        plt.text(i+1, v+1, f"{v:.2f}%", ha='center', fontsize=10)
        
    plt.tight_layout()
    plt.show()
    
    return fold_results

# Run the training
regnet_y_8gf_results = train_regnet_y_8gf_on_folds(num_epochs=10, batch_size=8)