"""
Model Training Functions for Clean 80-20 Split with 5-Fold Cross-Validation

This script contains functions for training and evaluating models using the
clean 80-20 split with 5-fold cross-validation approach.
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm

import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision import models

# Function to train a model for one epoch
def train_epoch(model, train_loader, criterion, optimizer, epoch, device):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    
    # Use tqdm for progress bar
    with tqdm(train_loader, desc=f'Epoch {epoch+1}', leave=False) as t:
        for inputs, labels in t:
            inputs, labels = inputs.to(device), labels.to(device)
            
            # Zero the parameter gradients
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            
            # Backward pass and optimize
            loss.backward()
            optimizer.step()
            
            # Track statistics
            running_loss += loss.item() * inputs.size(0)
            _, predicted = torch.max(outputs, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            
            # Update progress bar
            t.set_postfix(loss=loss.item(), acc=100.*correct/total)
    
    epoch_loss = running_loss / len(train_loader.dataset)
    epoch_acc = 100. * correct / total
    
    return epoch_loss, epoch_acc

# Function to evaluate the model
def evaluate(model, data_loader, criterion, device):
    model.eval()
    running_loss = 0.0
    correct = 0
    total = 0
    
    with torch.no_grad():
        for inputs, labels in data_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            
            # Track statistics
            running_loss += loss.item() * inputs.size(0)
            _, predicted = torch.max(outputs, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
    
    epoch_loss = running_loss / len(data_loader.dataset)
    epoch_acc = 100. * correct / total
    
    return epoch_loss, epoch_acc

# Function to train a model using cross-validation
def train_model_with_cv(cv_folds, train_full_dataset, test_loader, class_labels, 
                        create_fold_dataloaders, model_name='efficientnet_b0', 
                        num_epochs=10, device=torch.device('cuda')):
    # Results storage
    cv_results = []
    
    # Loop through each fold
    for fold_idx in range(len(cv_folds)):
        print(f"\n=== Training on Fold {fold_idx+1}/{len(cv_folds)} ===")
        
        # Create data loaders for this fold
        fold_train_loader, fold_val_loader = create_fold_dataloaders(fold_idx)
        
        # Initialize model
        if model_name == 'efficientnet_b0':
            model = models.efficientnet_b0(weights='DEFAULT')
            # Modify classifier for our number of classes
            num_ftrs = model.classifier[1].in_features
            model.classifier = nn.Sequential(
                nn.Dropout(p=0.2, inplace=True),
                nn.Linear(in_features=num_ftrs, out_features=len(class_labels))
            )
        elif model_name == 'resnet50':
            model = models.resnet50(weights='DEFAULT')
            # Modify classifier for our number of classes
            num_ftrs = model.fc.in_features
            model.fc = nn.Linear(num_ftrs, len(class_labels))
        elif model_name == 'googlenet':
            model = models.googlenet(weights='DEFAULT', aux_logits=True)
            # Modify classifier for our number of classes
            num_ftrs = model.fc.in_features
            model.fc = nn.Linear(num_ftrs, len(class_labels))
        else:
            raise ValueError(f"Unsupported model: {model_name}")
        
        # Move model to device
        model = model.to(device)
        
        # Loss function and optimizer
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        
        # Training loop
        best_val_acc = 0.0
        fold_results = {
            'train_loss': [], 'train_acc': [],
            'val_loss': [], 'val_acc': [],
            'best_epoch': 0, 'best_val_acc': 0.0
        }
        
        for epoch in range(num_epochs):
            # Train for one epoch
            train_loss, train_acc = train_epoch(model, fold_train_loader, criterion, optimizer, epoch, device)
            
            # Evaluate on validation set
            val_loss, val_acc = evaluate(model, fold_val_loader, criterion, device)
            
            # Print statistics
            print(f'Epoch {epoch+1}/{num_epochs}:')
            print(f'  Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%')
            print(f'  Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%')
            
            # Save statistics
            fold_results['train_loss'].append(train_loss)
            fold_results['train_acc'].append(train_acc)
            fold_results['val_loss'].append(val_loss)
            fold_results['val_acc'].append(val_acc)
            
            # Save best model
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                fold_results['best_epoch'] = epoch
                fold_results['best_val_acc'] = val_acc
                
                # Save model
                model_path = f'{model_name}_fold{fold_idx+1}.pth'
                torch.save(model.state_dict(), model_path)
                print(f'  Saved best model to {model_path}')
        
        # Evaluate on test set using best model
        # Load best model
        model_path = f'{model_name}_fold{fold_idx+1}.pth'
        model.load_state_dict(torch.load(model_path))
        
        # Evaluate on test set
        test_loss, test_acc = evaluate(model, test_loader, criterion, device)
        fold_results['test_loss'] = test_loss
        fold_results['test_acc'] = test_acc
        
        print(f'\nFold {fold_idx+1} Results:')
        print(f'  Best Validation Accuracy: {fold_results["best_val_acc"]:.2f}% (Epoch {fold_results["best_epoch"]+1})')
        print(f'  Test Accuracy: {test_acc:.2f}%')
        
        # Add to CV results
        cv_results.append(fold_results)
    
    # Calculate average results across all folds
    avg_val_acc = np.mean([fold['best_val_acc'] for fold in cv_results])
    avg_test_acc = np.mean([fold['test_acc'] for fold in cv_results])
    
    print('\n=== Cross-Validation Results ===')
    print(f'Average Best Validation Accuracy: {avg_val_acc:.2f}%')
    print(f'Average Test Accuracy: {avg_test_acc:.2f}%')
    
    # Plot learning curves
    plt.figure(figsize=(15, 10))
    
    # Plot training and validation accuracy
    plt.subplot(2, 1, 1)
    for i, fold_result in enumerate(cv_results):
        plt.plot(fold_result['train_acc'], '--', label=f'Fold {i+1} Train')
        plt.plot(fold_result['val_acc'], '-', label=f'Fold {i+1} Val')
    plt.title(f'{model_name} - Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy (%)')
    plt.legend()
    plt.grid(True)
    
    # Plot training and validation loss
    plt.subplot(2, 1, 2)
    for i, fold_result in enumerate(cv_results):
        plt.plot(fold_result['train_loss'], '--', label=f'Fold {i+1} Train')
        plt.plot(fold_result['val_loss'], '-', label=f'Fold {i+1} Val')
    plt.title(f'{model_name} - Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()
    
    return cv_results

# Function to create an ensemble model from the trained models
def create_ensemble_model(class_labels, model_name='efficientnet_b0', num_folds=5, device=torch.device('cuda')):
    models_list = []
    
    # Load all fold models
    for fold_idx in range(num_folds):
        # Initialize model
        if model_name == 'efficientnet_b0':
            model = models.efficientnet_b0(weights=None)
            # Modify classifier for our number of classes
            num_ftrs = model.classifier[1].in_features
            model.classifier = nn.Sequential(
                nn.Dropout(p=0.2, inplace=True),
                nn.Linear(in_features=num_ftrs, out_features=len(class_labels))
            )
        elif model_name == 'resnet50':
            model = models.resnet50(weights=None)
            # Modify classifier for our number of classes
            num_ftrs = model.fc.in_features
            model.fc = nn.Linear(num_ftrs, len(class_labels))
        elif model_name == 'googlenet':
            model = models.googlenet(weights=None, aux_logits=False)
            # Modify classifier for our number of classes
            num_ftrs = model.fc.in_features
            model.fc = nn.Linear(num_ftrs, len(class_labels))
        else:
            raise ValueError(f"Unsupported model: {model_name}")
        
        # Load model weights
        model_path = f'{model_name}_fold{fold_idx+1}.pth'
        if os.path.exists(model_path):
            model.load_state_dict(torch.load(model_path, map_location=device))
            model = model.to(device)
            model.eval()
            models_list.append(model)
            print(f"Loaded model from {model_path}")
        else:
            print(f"Warning: Model file {model_path} not found!")
    
    # Create ensemble prediction function
    def ensemble_predict(image_tensor):
        # Make predictions with each model
        predictions = []
        for model in models_list:
            with torch.no_grad():
                output = model(image_tensor)
                probabilities = F.softmax(output, dim=1)
                predictions.append(probabilities)
        
        # Average predictions
        ensemble_pred = torch.mean(torch.stack(predictions), dim=0)
        return ensemble_pred
    
    return ensemble_predict

# Function to evaluate ensemble model
def evaluate_ensemble(ensemble_predict_fn, data_loader, device):
    correct = 0
    total = 0
    
    with torch.no_grad():
        for inputs, labels in data_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            
            # Get ensemble predictions
            outputs = ensemble_predict_fn(inputs)
            _, predicted = torch.max(outputs, 1)
            
            # Track statistics
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
    
    accuracy = 100. * correct / total
    return accuracy
