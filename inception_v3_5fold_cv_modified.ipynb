{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Inception-v3: 5-Fold Cross-Validation with Confusion Matrix per Fold\n", "\n", "This notebook implements a clean 80-20 train-test split followed by 5-fold cross-validation with **no data reuse**.\n", "Each fold contains a completely separate subset of the training data.\n", "A confusion matrix is generated after each fold, and final average train and test accuracies are reported.\n", "\n", "**Model: Inception-v3**"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import os\n", "import re\n", "import random\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from PIL import Image\n", "from tqdm.notebook import tqdm\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Import PyTorch components\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch.utils.data import Dataset, DataLoader, Subset\n", "from torchvision import transforms, models\n", "\n", "# Import scikit-learn for splitting and metrics\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import confusion_matrix, classification_report\n", "import seaborn as sns\n", "\n", "# Set random seeds for reproducibility\n", "random.seed(42)\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "if torch.cuda.is_available():\n", "    torch.cuda.manual_seed_all(42)\n", "    torch.backends.cudnn.benchmark = True  # Speed up training"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: cuda\n"]}], "source": ["# Device configuration\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 4 classes: ['Cy<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>mor']\n", "Class 'Cyst': 3284 images\n", "Class 'Normal': 5002 images\n", "Class 'Stone': 1360 images\n", "Class 'Tumor': 2283 images\n", "\n", "Total images in dataset: 11929\n"]}], "source": ["# Define Dataset Path\n", "DATASET_PATH = \"C:/Users/<USER>/OneDrive/Desktop/Data Science/New Example/CT Scan/CT-KIDNEY-DATASET-Normal-Cyst-<PERSON><PERSON>-Stone/Resized\"\n", "\n", "# Get class labels (folder names)\n", "class_labels = sorted([d for d in os.listdir(DATASET_PATH) if os.path.isdir(os.path.join(DATASET_PATH, d))])\n", "\n", "# Print basic info\n", "print(f\"Found {len(class_labels)} classes: {class_labels}\")\n", "\n", "# Dictionary to store image file paths\n", "image_files = {label: [] for label in class_labels}\n", "\n", "# Read images from each class\n", "for label in class_labels:\n", "    class_dir = os.path.join(DATASET_PATH, label)\n", "    image_files[label] = [os.path.join(class_dir, img) for img in os.listdir(class_dir) if img.endswith(('.png', '.jpg', '.jpeg'))]\n", "\n", "# Print number of images per class\n", "total_images = 0\n", "for label, files in image_files.items():\n", "    print(f\"Class '{label}': {len(files)} images\")\n", "    total_images += len(files)\n", "    \n", "print(f\"\\nTotal images in dataset: {total_images}\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# Function to extract patient ID from filename\n", "def get_patient_id(filename):\n", "    # Extract patient ID from filename using regex\n", "    match = re.search(r'(\\d+)', os.path.basename(filename))\n", "    return match.group(1) if match else os.path.basename(filename)\n", "\n", "# Custom dataset class with patient ID tracking\n", "class KidneyCTDataset(Dataset):\n", "    def __init__(self, root_dir, transform=None):\n", "        self.root_dir = root_dir\n", "        self.transform = transform\n", "        self.classes = sorted([d for d in os.listdir(root_dir) if os.path.isdir(os.path.join(root_dir, d))])\n", "        self.class_to_idx = {cls: i for i, cls in enumerate(self.classes)}\n", "        \n", "        self.samples = []\n", "        self.patient_to_samples = {}\n", "        \n", "        # Load all image paths and organize by patient ID\n", "        for class_name in self.classes:\n", "            class_dir = os.path.join(root_dir, class_name)\n", "            for img_name in os.listdir(class_dir):\n", "                if img_name.endswith(('.png', '.jpg', '.jpeg')):\n", "                    img_path = os.path.join(class_dir, img_name)\n", "                    patient_id = get_patient_id(img_path)\n", "                    \n", "                    # Add to samples list\n", "                    idx = len(self.samples)\n", "                    self.samples.append((img_path, self.class_to_idx[class_name]))\n", "                    \n", "                    # Add to patient mapping\n", "                    if patient_id not in self.patient_to_samples:\n", "                        self.patient_to_samples[patient_id] = []\n", "                    self.patient_to_samples[patient_id].append(idx)\n", "    \n", "    def __len__(self):\n", "        return len(self.samples)\n", "    \n", "    def __getitem__(self, idx):\n", "        img_path, label = self.samples[idx]\n", "        \n", "        # Load image\n", "        image = Image.open(img_path).convert('RGB')\n", "        \n", "        # Apply transformations\n", "        if self.transform:\n", "            image = self.transform(image)\n", "        \n", "        return image, label"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total patients: 5077\n", "Total images: 11929\n"]}], "source": ["# Define transformations\n", "transform = transforms.Compose([\n", "    transforms.Resize((299, 299)),  # Inception-v3 expects 299x299 input\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])\n", "])\n", "\n", "# Load dataset\n", "dataset = KidneyCTDataset(root_dir=DATASET_PATH, transform=transform)\n", "\n", "# Get all patient IDs\n", "patient_ids = list(dataset.patient_to_samples.keys())\n", "print(f\"Total patients: {len(patient_ids)}\")\n", "print(f\"Total images: {len(dataset)}\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train-Test Split (by patient):\n", "  Train: 4061 patients (80.0% of all patients)\n", "  Test: 1016 patients (20.0% of all patients)\n", "\n", "Train-Test Split (by image):\n", "  Train: 9529 images (79.9% of all images)\n", "  Test: 2400 images (20.1% of all images)\n"]}], "source": ["# Step 1: Perform a clean 80-20 split by patient ID\n", "train_patients, test_patients = train_test_split(\n", "    patient_ids, \n", "    test_size=0.2, \n", "    random_state=42\n", ")\n", "\n", "print(f\"Train-Test Split (by patient):\")\n", "print(f\"  Train: {len(train_patients)} patients ({len(train_patients)/len(patient_ids):.1%} of all patients)\")\n", "print(f\"  Test: {len(test_patients)} patients ({len(test_patients)/len(patient_ids):.1%} of all patients)\")\n", "\n", "# Get sample indices for each split\n", "train_indices = [idx for patient in train_patients for idx in dataset.patient_to_samples[patient]]\n", "test_indices = [idx for patient in test_patients for idx in dataset.patient_to_samples[patient]]\n", "\n", "print(f\"\\nTrain-Test Split (by image):\")\n", "print(f\"  Train: {len(train_indices)} images ({len(train_indices)/len(dataset):.1%} of all images)\")\n", "print(f\"  Test: {len(test_indices)} images ({len(test_indices)/len(dataset):.1%} of all images)\")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "5-Fold Split (No Data Reuse):\n", "Fold 1: 813 patients, 1931 images\n", "Fold 2: 812 patients, 1884 images\n", "Fold 3: 812 patients, 1937 images\n", "Fold 4: 812 patients, 1879 images\n", "Fold 5: 812 patients, 1898 images\n", "✓ No overlap between fold 1 and fold 2\n", "✓ No overlap between fold 1 and fold 3\n", "✓ No overlap between fold 1 and fold 4\n", "✓ No overlap between fold 1 and fold 5\n", "✓ No overlap between fold 2 and fold 3\n", "✓ No overlap between fold 2 and fold 4\n", "✓ No overlap between fold 2 and fold 5\n", "✓ No overlap between fold 3 and fold 4\n", "✓ No overlap between fold 3 and fold 5\n", "✓ No overlap between fold 4 and fold 5\n"]}], "source": ["# Step 2: Divide the training patients into 5 completely separate folds\n", "# Shuffle the training patients\n", "random.shuffle(train_patients)\n", "\n", "# Split into 5 equal groups\n", "num_folds = 5\n", "fold_size = len(train_patients) // num_folds\n", "fold_patients = [train_patients[i*fold_size:(i+1)*fold_size] for i in range(num_folds)]\n", "\n", "# Handle any remaining patients\n", "for i in range(len(train_patients) % num_folds):\n", "    fold_patients[i].append(train_patients[num_folds*fold_size + i])\n", "\n", "# Create a list to store fold information\n", "folds = []\n", "\n", "print(\"\\n5-Fold Split (No Data Reuse):\")\n", "for fold_idx in range(num_folds):\n", "    # Get sample indices for this fold\n", "    fold_indices = [idx for patient in fold_patients[fold_idx] for idx in dataset.patient_to_samples[patient]]\n", "    \n", "    # Store fold information\n", "    folds.append({\n", "        'patients': fold_patients[fold_idx],\n", "        'indices': fold_indices\n", "    })\n", "    \n", "    print(f\"Fold {fold_idx+1}: {len(fold_patients[fold_idx])} patients, {len(fold_indices)} images\")\n", "\n", "# Verify no overlap between folds\n", "for i in range(num_folds):\n", "    for j in range(i+1, num_folds):\n", "        overlap = set(folds[i]['indices']).intersection(set(folds[j]['indices']))\n", "        if len(overlap) > 0:\n", "            print(f\"❌ Overlap between fold {i+1} and fold {j+1}: {len(overlap)} images\")\n", "        else:\n", "            print(f\"✓ No overlap between fold {i+1} and fold {j+1}\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fold dataset sizes:\n", "  Fold 1: 1931 images\n", "  Fold 2: 1884 images\n", "  Fold 3: 1937 images\n", "  Fold 4: 1879 images\n", "  Fold 5: 1898 images\n", "  Test: 2400 images\n"]}], "source": ["# Create test dataset\n", "test_dataset = Subset(dataset, test_indices)\n", "\n", "# Create test data loader (same for all folds)\n", "test_loader = DataLoader(\n", "    test_dataset, \n", "    batch_size=8, \n", "    shuffle=False, \n", "    num_workers=0,\n", "    pin_memory=True\n", ")\n", "\n", "# Create fold datasets\n", "fold_datasets = [Subset(dataset, fold['indices']) for fold in folds]\n", "\n", "# Print fold sizes\n", "print(\"Fold dataset sizes:\")\n", "for i, fold_dataset in enumerate(fold_datasets):\n", "    print(f\"  Fold {i+1}: {len(fold_dataset)} images\")\n", "print(f\"  Test: {len(test_dataset)} images\")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# Function to train a model for one epoch\n", "def train_epoch(model, train_loader, criterion, optimizer, epoch):\n", "    model.train()\n", "    running_loss = 0.0\n", "    correct = 0\n", "    total = 0\n", "    \n", "    # Use tqdm for progress bar\n", "    with tqdm(train_loader, desc=f'Epoch {epoch+1}', leave=False) as t:\n", "        for inputs, labels in t:\n", "            inputs, labels = inputs.to(device), labels.to(device)\n", "            \n", "            # Zero the parameter gradients\n", "            optimizer.zero_grad()\n", "            \n", "            # Forward pass\n", "            outputs = model(inputs)\n", "            \n", "            # Handle Inception-v3's special output format when aux_logits=True\n", "            if isinstance(outputs, tuple):\n", "                # Inception-v3 with aux_logits returns (output, aux_output)\n", "                main_output = outputs[0]\n", "                aux_output = outputs[1]\n", "                \n", "                # Calculate main loss\n", "                loss = criterion(main_output, labels)\n", "                \n", "                # Add auxiliary loss with weight 0.3\n", "                loss += 0.3 * criterion(aux_output, labels)\n", "            else:\n", "                # Normal case for other models or when aux_logits=False\n", "                main_output = outputs\n", "                loss = criterion(main_output, labels)\n", "            \n", "            # Backward pass and optimize\n", "            loss.backward()\n", "            optimizer.step()\n", "            \n", "            # Track statistics (using main output for accuracy)\n", "            running_loss += loss.item() * inputs.size(0)\n", "            _, predicted = torch.max(main_output, 1)\n", "            total += labels.size(0)\n", "            correct += (predicted == labels).sum().item()\n", "            \n", "            # Update progress bar\n", "            t.set_postfix(loss=loss.item(), acc=100.*correct/total)\n", "    \n", "    epoch_loss = running_loss / len(train_loader.dataset)\n", "    epoch_acc = 100. * correct / total\n", "    \n", "    return epoch_loss, epoch_acc\n", "\n", "# Function to evaluate the model\n", "def evaluate(model, data_loader, criterion):\n", "    model.eval()\n", "    running_loss = 0.0\n", "    correct = 0\n", "    total = 0\n", "    \n", "    with torch.no_grad():\n", "        for inputs, labels in data_loader:\n", "            inputs, labels = inputs.to(device), labels.to(device)\n", "            \n", "            # Forward pass\n", "            outputs = model(inputs)\n", "            \n", "            # Handle Inception-v3's special output format\n", "            if isinstance(outputs, tuple):\n", "                # Use only the main output\n", "                main_output = outputs[0]\n", "                loss = criterion(main_output, labels)\n", "            else:\n", "                main_output = outputs\n", "                loss = criterion(main_output, labels)\n", "            \n", "            # Track statistics\n", "            running_loss += loss.item() * inputs.size(0)\n", "            _, predicted = torch.max(main_output, 1)\n", "            total += labels.size(0)\n", "            correct += (predicted == labels).sum().item()\n", "    \n", "    epoch_loss = running_loss / len(data_loader.dataset)\n", "    epoch_acc = 100. * correct / total\n", "    \n", "    return epoch_loss, epoch_acc"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# Function to generate predictions and true labels\n", "def get_predictions(model, data_loader, device):\n", "    model.eval()\n", "    all_preds = []\n", "    all_labels = []\n", "    \n", "    with torch.no_grad():\n", "        for inputs, labels in data_loader:\n", "            inputs, labels = inputs.to(device), labels.to(device)\n", "            \n", "            # Forward pass\n", "            outputs = model(inputs)\n", "            \n", "            # Handle Inception-v3's special output format\n", "            if isinstance(outputs, tuple):\n", "                # Use only the main output\n", "                outputs = outputs[0]\n", "                \n", "            _, predicted = torch.max(outputs, 1)\n", "            \n", "            # Collect predictions and labels\n", "            all_preds.extend(predicted.cpu().numpy())\n", "            all_labels.extend(labels.cpu().numpy())\n", "    \n", "    return np.array(all_preds), np.array(all_labels)\n", "\n", "# Function to plot confusion matrix\n", "def plot_confusion_matrix(cm, classes, normalize=False, title='Confusion Matrix', cmap=plt.cm.Blues):\n", "    plt.figure(figsize=(10, 8))\n", "    \n", "    if normalize:\n", "        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]\n", "        fmt = '.2f'\n", "        title = 'Normalized ' + title\n", "    else:\n", "        fmt = 'd'\n", "    \n", "    # Create heatmap\n", "    sns.heatmap(cm, annot=True, fmt=fmt, cmap=cmap, \n", "                xticklabels=classes, yticklabels=classes, \n", "                square=True, cbar=True)\n", "    \n", "    plt.title(title, fontsize=16)\n", "    plt.ylabel('True Label', fontsize=12)\n", "    plt.xlabel('Predicted Label', fontsize=12)\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# Function to initialize Inception-v3 model\n", "def initialize_inception_v3():\n", "    # Load pre-trained Inception-v3 model with auxiliary outputs enabled\n", "    # Note: aux_logits must be True when using pre-trained weights\n", "    model = models.inception_v3(weights='DEFAULT', aux_logits=True)\n", "    \n", "    # Modify the final fully connected layer for our number of classes\n", "    num_ftrs = model.fc.in_features\n", "    model.fc = nn.Linear(num_ftrs, len(class_labels))\n", "    \n", "    # Also modify the auxiliary classifier layer\n", "    model.AuxLogits.fc = nn.Linear(model.AuxLogits.fc.in_features, len(class_labels))\n", "    \n", "    return model.to(device)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["# Function to train Inception-v3 models on each fold\n", "def train_inception_v3_on_folds(num_epochs=10, batch_size=16):  # Increased batch size to 16\n", "    # Results storage\n", "    fold_results = []\n", "    all_train_accs = []\n", "    all_test_accs = []\n", "    \n", "    # Loop through each fold\n", "    for fold_idx in range(num_folds):\n", "        print(f\"\\n=== Training Inception-v3 on Fold {fold_idx+1}/{num_folds} ===\\n\")\n", "        \n", "        # Create data loader for this fold\n", "        train_loader = DataLoader(\n", "            fold_datasets[fold_idx],\n", "            batch_size=batch_size,\n", "            shuffle=True,\n", "            num_workers=0,\n", "            pin_memory=True\n", "        )\n", "        \n", "        print(f\"Training on {len(fold_datasets[fold_idx])} images\")\n", "        \n", "        # Initialize Inception-v3 model\n", "        model = initialize_inception_v3()\n", "        \n", "        # Loss function and optimizer\n", "        criterion = nn.CrossEntropyLoss()\n", "        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)\n", "        \n", "        # Training loop\n", "        results = {\n", "            'train_loss': [], 'train_acc': [],\n", "            'test_loss': 0, 'test_acc': 0,\n", "            'final_train_acc': 0\n", "        }\n", "        \n", "        for epoch in range(num_epochs):\n", "            # Train for one epoch\n", "            train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, epoch)\n", "            \n", "            # Print statistics\n", "            print(f'Epoch {epoch+1}/{num_epochs}:')\n", "            print(f'  Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%')\n", "            \n", "            # Save statistics\n", "            results['train_loss'].append(train_loss)\n", "            results['train_acc'].append(train_acc)\n", "            \n", "            # Save the final train accuracy for the last epoch\n", "            if epoch == num_epochs - 1:\n", "                results['final_train_acc'] = train_acc\n", "                all_train_accs.append(train_acc)\n", "        \n", "        # Set aux_logits to False for inference\n", "        model.aux_logits = False\n", "        \n", "        # Save the final model\n", "        model_path = f'inception_v3_fold{fold_idx+1}.pth'\n", "        torch.save(model.state_dict(), model_path)\n", "        print(f'Saved final model to {model_path}')\n", "        \n", "        # Evaluate on test set after training is complete\n", "        test_loss, test_acc = evaluate(model, test_loader, criterion)\n", "        results['test_loss'] = test_loss\n", "        results['test_acc'] = test_acc\n", "        all_test_accs.append(test_acc)\n", "        \n", "        print(f'\\nFold {fold_idx+1} Results:')\n", "        print(f'  Final Train Acc: {results[\"final_train_acc\"]:.2f}%')\n", "        print(f'  Test Loss: {test_loss:.4f}, Test Acc: {test_acc:.2f}%')\n", "        \n", "        # Generate confusion matrix for this fold\n", "        print(f\"\\nGenerating confusion matrix for Fold {fold_idx+1}...\")\n", "        y_pred, y_true = get_predictions(model, test_loader, device)\n", "        cm = confusion_matrix(y_true, y_pred)\n", "        \n", "        print(f\"Confusion Matrix for Fold {fold_idx+1}:\")\n", "        print(cm)\n", "        \n", "        # Generate classification report\n", "        report = classification_report(y_true, y_pred, target_names=class_labels)\n", "        print(f\"\\nClassification Report for Fold {fold_idx+1}:\")\n", "        print(report)\n", "        \n", "        # Plot confusion matrices\n", "        plot_confusion_matrix(cm, class_labels, normalize=False, \n", "                             title=f'Inception-v3 Fold {fold_idx+1} Confusion Matrix')\n", "        plot_confusion_matrix(cm, class_labels, normalize=True, \n", "                             title=f'Inception-v3 Fold {fold_idx+1} Normalized Confusion Matrix')\n", "        \n", "        # Add to fold results\n", "        fold_results.append(results)\n", "    \n", "    # Calculate average results across all folds\n", "    avg_train_acc = np.mean(all_train_accs)\n", "    avg_test_acc = np.mean(all_test_accs)\n", "    \n", "    print('\\n=== Overall Inception-v3 Results ===')\n", "    print(f'Average Train Accuracy: {avg_train_acc:.2f}%')\n", "    print(f'Average Test Accuracy: {avg_test_acc:.2f}%')\n", "    \n", "    # Plot training accuracy\n", "    plt.figure(figsize=(15, 5))\n", "    for i, fold_result in enumerate(fold_results):\n", "        plt.plot(fold_result['train_acc'], '-', label=f'Fold {i+1} Train')\n", "    plt.title(f'Inception-v3 - Training Accuracy', fontsize=14)\n", "    plt.xlabel('Epoch', fontsize=12)\n", "    plt.ylabel('Accuracy (%)', fontsize=12)\n", "    plt.legend()\n", "    plt.grid(True)\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Display final test accuracies\n", "    plt.figure(figsize=(10, 6))\n", "    test_accs = [fold['test_acc'] for fold in fold_results]\n", "    plt.bar(range(1, num_folds+1), test_accs, color='#3498db')\n", "    plt.axhline(y=avg_test_acc, color='r', linestyle='-', label=f'Average: {avg_test_acc:.2f}%')\n", "    plt.title(f'Inception-v3 - Test Accuracy by Fold', fontsize=14)\n", "    plt.xlabel('Fold', fontsize=12)\n", "    plt.ylabel('Test Accuracy (%)', fontsize=12)\n", "    plt.xticks(range(1, num_folds+1))\n", "    plt.legend()\n", "    plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "    \n", "    # Add value labels\n", "    for i, v in enumerate(test_accs):\n", "        plt.text(i+1, v+1, f\"{v:.2f}%\", ha='center', fontsize=10)\n", "        \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Display final train accuracies\n", "    plt.figure(figsize=(10, 6))\n", "    train_accs = [fold['final_train_acc'] for fold in fold_results]\n", "    plt.bar(range(1, num_folds+1), train_accs, color='#2ecc71')\n", "    plt.axhline(y=avg_train_acc, color='r', linestyle='-', label=f'Average: {avg_train_acc:.2f}%')\n", "    plt.title(f'Inception-v3 - Final Train Accuracy by Fold', fontsize=14)\n", "    plt.xlabel('Fold', fontsize=12)\n", "    plt.ylabel('Train Accuracy (%)', fontsize=12)\n", "    plt.xticks(range(1, num_folds+1))\n", "    plt.legend()\n", "    plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "    \n", "    # Add value labels\n", "    for i, v in enumerate(train_accs):\n", "        plt.text(i+1, v+1, f\"{v:.2f}%\", ha='center', fontsize=10)\n", "        \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return fold_results"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Training Inception-v3 on Fold 1/5 ===\n", "\n", "Training on 1931 images\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e0a10e7e0cec43faa2a6c68859daee4c", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 1:   0%|          | 0/121 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 1/10:\n", "  Train Loss: 0.7176, Train Acc: 78.66%\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "048fc48deb4b46dd94986deaa44ed7e3", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 2:   0%|          | 0/121 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 2/10:\n", "  Train Loss: 0.3479, Train Acc: 90.47%\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "087d5e6006e142dc9559aadcfc201db2", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 3:   0%|          | 0/121 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 3/10:\n", "  Train Loss: 0.2412, Train Acc: 93.73%\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8664be5219f44d1f99acf80f8f0f5c79", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 4:   0%|          | 0/121 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 4/10:\n", "  Train Loss: 0.1159, Train Acc: 97.31%\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fbfd6b33e4d4445b99daed6158889185", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 5:   0%|          | 0/121 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 5/10:\n", "  Train Loss: 0.1388, Train Acc: 96.53%\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "72932ae6f23e48f3aa40a89282813879", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 6:   0%|          | 0/121 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 6/10:\n", "  Train Loss: 0.1123, Train Acc: 97.57%\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b6097dc6abc04f219804bae3c5ac6d1f", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 7:   0%|          | 0/121 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 7/10:\n", "  Train Loss: 0.1151, Train Acc: 97.46%\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9f880c985fe845438dca1dbb686f1023", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 8:   0%|          | 0/121 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 8/10:\n", "  Train Loss: 0.1057, Train Acc: 97.67%\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "92d02810eed94957998c48955eda7e30", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 9:   0%|          | 0/121 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 9/10:\n", "  Train Loss: 0.0575, Train Acc: 98.71%\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7a2cd58a642a4267b880e8a0db8e9074", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 10:   0%|          | 0/121 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 10/10:\n", "  Train Loss: 0.0775, Train Acc: 98.19%\n", "Saved final model to inception_v3_fold1.pth\n", "\n", "Fold 1 Results:\n", "  Final Train Acc: 98.19%\n", "  Test Loss: 0.1683, Test Acc: 96.42%\n", "\n", "Generating confusion matrix for Fold 1...\n", "Confusion Matrix for Fold 1:\n", "[[608  13  38   2]\n", " [  0 998   0   0]\n", " [  0   3 271   1]\n", " [  6  23   0 437]]\n", "\n", "Classification Report for Fold 1:\n", "              precision    recall  f1-score   support\n", "\n", "        Cyst       0.99      0.92      0.95       661\n", "      Normal       0.96      1.00      0.98       998\n", "       Stone       0.88      0.99      0.93       275\n", "       <PERSON><PERSON>       0.99      0.94      0.96       466\n", "\n", "    accuracy                           0.96      2400\n", "   macro avg       0.96      0.96      0.96      2400\n", "weighted avg       0.97      0.96      0.96      2400\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA24AAAMWCAYAAACJINXLAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjEsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvc2/+5QAAAAlwSFlzAAAPYQAAD2EBqD+naQAAhxNJREFUeJzt3Qd4U1UbwPG3ZYOMQikgW4SyN7L3EhQZCjJky5K9ZO9PEVBkD1FRlqCIG0EEFJUlIFOWMmXvPdt8z3swsWlTaEqb3LT/n8+15OYmOTc5ubnvPe85x89ms9kEAAAAAGBZ/t4uAAAAAADg4QjcAAAAAMDiCNwAAAAAwOII3AAAAADA4gjcAAAAAMDiCNwAAAAAwOII3AAAAADA4gjcAAAAAMDiCNwAAAAAwOII3B4iR44c4ufnZ5alS5dGul2NGjXMNh999JHER23atHG5/3pb1+v9VlSlShVTvp9++inKjxk5cqR5jD4WDxw5csS8J/p9iWv+/PNP6dWrl1SqVEmyZcsmyZMnl2TJksnTTz8t7du3l127drn9nFrf7MeVhy2XL1+Ole/lo0T3e7tu3Tp588035cUXX3Q6dv76668SXRyDoyY+HYPDOn78uAwdOlTKlCkj6dOnl0SJEkmaNGmkePHi0rNnT/n999/F2+7cuSODBw+W3LlzS5IkSbxyrIzuscCT7L+tuuhnee/evUi3PXXqlCRMmNCx/YIFCyQu1WvgYRI+9F44DBkyRBo0aGAOFkB8oicZR48elcOHD8fJ4Oxh1q9fL5MnT5YMGTJIcHCwlC1bVm7evGkCtg8//FDmzZtnlmbNmkXr+Vu3bh3pfYkTJxZf0qNHD9mxY0esPT/HYIQ1fvx4GTZsmNy9e1eeeOIJKV26tAQFBcm1a9fM93PKlClm6d+/v9nWW7SMEyZMMMeQ+vXrm4s/gYGBXiuPLzh//rx8/fXX5iKQKx9//LGEhITESsD1888/y9q1a7k4C8viFzAK9EB74MABef/996Vz587eLo7PaNiwobkSmjp1am8XBbEoc+bMsnfvXnO1O67Rlhzdt7x58zqtDw0NlYkTJ5qTwldffVWeffZZCQgIcPv5rXwF3F01a9Y033lt7dClfPnyJuCPCRyDoyeuHoMHDhwo48aNM8ect99+W7p162Zas8LauHGjCfa13njTp59+av7+8ssvptXNG8aOHWves0yZMonVlSxZUrZs2WIujEUWuM2dO9d83noxbefOnWI1ejFPL/BplgYQ00iVjAJNuVCjR482X0ZEjZ4s6AmvL/xYIPr05Ek/51y5cklcoy2M4YM25e/vL/369ZOnnnrKHBMeJx0wrtBWhREjRki9evVMMB+TOAZHT1w8Bq9evdoEbWrJkiXSt2/fCEGb0oD1xx9/NPd707Fjx8xfbwVtSj9/rQe+EMAXKVLEXPhZuXKlnDx5MsL9GgBrMK6t79G5WOYJGrDp+60XnICYRuAWBXXr1pXKlSubvOp3333X7ccvXrxYqlevLmnTpjU/MNmzZ5d27dpFeiXQ3q9D+w599dVXUq1aNfPYsDnT9txupfndzzzzjEkX0dxwTduy/1jYbDaZNm2aFC1aVFKkSGFSNDTf/ezZsxFeV3PK9blatGhhDjqpUqUy/Xn0qpamQbk6iD5MZP0rotK/R/Pdw9u6daspmx4U9X3U96R27dqyfPnyh/aB0Pdaf7iSJk1qfjz1KuytW7cktvp56Xv+3nvvSYkSJcx7rj+WtWrVkg0bNkT6eD0ZnTRpklSoUMH8GNnriZ4EL1q0yOVjtM+PtvToZ65pdXqy/Morr5h+WQ8r3/37903qUIECBcznq3WiSZMmsm/fPpefn73VJGfOnE6fkb0uPqqP2z///CPdu3c3771+Bvp+aGvM7NmzXaa7hK03N27ckEGDBpk+ZfqeZMyY0aQXnjhxQqJKXyNLlizmOfUqfGQ0ENNtevfuHeXntqftuTpxjGl6IvP888+bdDD9vJ988kl5+eWXzdVpd2kd0PpWqFAh85loHdKr29Hps+cJHIM5Btv973//M39feOEF06L4MLofFStWjLBej3Vt27Y19cC+H1o/7K1jkfW/0r/nzp2Trl27StasWc33UP/q8S18n1R7HdLP314W+2JvaX9U37OH9VH87LPPTEZAunTpzMUz/Zs/f37p0KFDhFaoR73O43w/NK1Qf9/0d0vrqgZd2uL0OPS19bitKZHhaUucfZvIaLrsnDlzpFGjRqa+6fdOFz3ead0L/1nZ+x1rmqSqWrWqy88r7G+dlk+zLooVK2a+9/ZjQWR93PS3R+uLvkfbt2+PUGb9zDTQ08/yt99+i9b7hnjChkhlz55dj7i2X375xbZx40bz71SpUtnOnz/vtF316tXNfXPnznVaHxoaamvVqpW5L2HChLZq1arZmjZtasuTJ49Zlzx5ctv3338f6et269bN/C1ZsqStWbNmtsqVK9vWrVtnttH1ugwcONDx3C+99JItW7ZsZn3WrFltFy9etDVp0sSWNGlS27PPPmtr2LChLSgoyNxfuHBh2507d5xe9/jx4+a+1KlT28qUKWNr3LixrW7durYnn3zSrE+fPr3t4MGDEcrbunVrl/uvt3W93h9++8iWZMmSmceMHj3a6TGTJk2y+fv7m/uKFi1q9rVChQq2xIkTm3WjRo2KUK69e/c69jdTpkyO/dHXKFu2rFn0vrVr19qiasSIEeYx+lmEdfjwYbNePzvdj0SJEpnPRN9/++edJEkSU4/CO3bsmC1//vyOOlGzZk1TTypWrGg+C33OsO7du2ee1/6c5cqVM/tWpEgRs073L3y9Clu+Ro0amfLVqFHDvM5TTz1l7nviiSds69evdzxG673uS4oUKcz9L774otNnpe9v+OcOb/Pmzba0adOa+7Vuvvzyy6Yuap3UdbVr145QD+31pkGDBqaepkmTxlavXj1b/fr1HZ+nvtbly5ej/LkNGjTIPK5Tp04u79f3NEOGDGabnTt3Ruk5Z8+ebbbXMl25ciXKZdH6Zv/+RtXQoUPN9n5+frby5cub44F+D3RdggQJbB988EGUv5chISHmvdX79PtTq1Yt87nkyJHDfC6vvfaay+/t4xw/Y+I5OAZzDL506ZKjDJ9//rktOr799lvH8Sc4ONjUB/3s9Huk69q1axfpcV/vy5IlizlW6HFU90U/K72vVKlStrt37zoe07dvX8fnYv8M7Iv9OxHZ5/aoz0/fa3udrlSpkqmbWpaCBQuaY8S7777rtH1kr/O4349hw4aZ1ytRooR5nNZZ+/6GL8Oj2N/j9u3bm++Nfka5c+d22ubq1avm90i/Y3oc0++jPmb+/PlO2+n7a/++aB3V45se59KlS2fWP/30007HEK2n+h7ZfwP0d8nV52X/rdPXf+GFF0zd12OPvv/6fbazlyt8vX7nnXfMet0v3Zew+2V/z8ePH+/W+4b4h8DNjRMPPVDr7d69e0fppGHmzJlmfWBgoO2PP/5wOljaD1J6Unr27FmXr6s/JF999ZXLstkPjnog2r59u2P9zZs3zYFK7ytUqJAtV65ctiNHjjjuP3funDlo6f0LFixwek49eOjrhT+Z0B8j+4mv/jg87klDZAYPHuz4Mb1w4YJj/YoVK8yPg76PP//8s9Nj9CRbf0j1cT/99JPTffpDquv1xOnWrVuO9UePHjXvi/09jMnAzR5U7N+/33Hf/fv3zQ++3qc/HmHpj4+eFNrvC18XtNzfffedy/epdOnStkOHDjnd99lnn5l6ExAQYE5yXJVP38cdO3Y4la979+6Ost++fdtlfdTncCWywE2fx/7Yzp07O53U/P333yZQ0Pt0f1zVG/sPaNigSH/Q7QHLm2++aYuqAwcOOL5vYeuCndZ7vV9PQFy5ceOG40dcgx77d0h/6O0n8rEVuOmJk26rJzI//PCD033vv/++uU8D8d27d0fpezlt2jRH2f/880+n4LVLly5OJ5pWCtwUx+D4fQxevXq14zF6wctdp0+fdgRa//vf/0w9sPv999/NcVPve++995weZ68rurRp08bpGKnlyJw5s7lv0aJFEV7zYd/16ARu+toa+OqFtn379kV4jNY1+0W1R73O434/9LjzzTffuCyzvs/6XYhO4KY0GNLbYY+vc+bMMeuGDx9ubkcWuOkFkB9//NH8voY/jtsDVb1AFV5kAZer31Gt82F/56P6PPaLZhro2um/dd3zzz/vVCcBVwjc3Dhp0IOkXpXSVo6wP8SRnTTYf5imTJkS4bn1y6lXaPT+N954w+XrurryZ2c/eEyfPj3CfcuWLXPcH/6kP+xVn7Zt29rcoVd99Wpn2CtFMXXSYG+90JPJ8MGIBih639KlS10+9tNPP3W0CNn9+uuvZp1enQt/dV598cUXsRa4ff311xEed+rUKXOf1p2wAcyXX37puBp97dq1R76+nkzpj7aexP/zzz8ut7G3mEydOtVl+fTKeXh6MmA/+Vi4cGGMBG76Y6rrtd6EDwaVfp56f8qUKZ1O6uz1Rj+7kydPRnjc4sWLzf16ddgd2oIZ2cmV/cdUgxpXNAi2v3/2RVsq3ak7rgK3yJaw3yX78aVPnz4un09/7PX+Dh06ROl7aQ8a9KQtPP0cMmbMaNnAjWNw/D4G27/7urg6pjzKmDFjHnqB5u233zb3h2/psR/39WRdT/7De+uttyKtLzEduGkQpevCtvA8SmSv87jfj8iOSXnz5o0QdLkbuK1atcoRKNtpi55eQLD/FkUWuD2Mfn56DNHWuMcJ3ObNmxfpazzsefS3JGfOnOb+GTNmmMXeihf2YgkQGfq4uUH7GWhetc7LokP8Poz26/n7778jHfJb8581x15pjrgrL730UpT6foRn7wSt/W809zyy+yPrL6FDemvutubt6/5qfrwu2i9GR9P766+/JCZ999138tprr5kc9G+//db0pQo7LPDmzZtNXrj293LFPmyvDt1uZ88t1z5gmvsfng7LHBsdtfU919cMT/tmaR8ArTsXLlxwrF+xYoX527x5c5Mn/yhaV7RviPYRi2wACFfvR1iu6qP2a9D+Uiqm5p6xP0/Tpk1d9gHT/gf6nmh/BO0742p0MVeDKuTLl8/8daefm7J/38L389A+K1oHtYz6Obiic0P9e6FLTp8+bfr0aF8m7QuhI0tGl34Wrhbt06f0O2fv7xDZXFw6n9zDjiNh6Xtm//5qf8jwtA+S9ne0Ko7BHIMfh71MkU3DYf8uHTx40OVno/3AXA04Ed1jUnTocUf7WGmfKB14xVWf5qiIie9HZPUhJt4Pfa+1r5325bt+/boZ3Vf7iekxN6rT0mh91IFstE+i7ot+h7Sea18zPe5funQp2uWLbMTLR9HfEu1LqWXQ/tS6aL82Xad9DIFHYToAN2nnZO08vnDhQjOYQeHChV1uZz9g6Q+WdjB3xT4KX2QHt6gcnFwNN2sPAPSk19WcRylTpjR/b9++7bReB4Jo2bKlfPHFFw99zatXr0pM0RN2e8CgI4TpyXpYOneYnixrsPKoASD0QBz2R0mFPQEJy97BOKbnndL3PLJh8bUe6A9F2PfdPvCHq5ELXTl06JBjZLWwnaEf9X6E/dHQxRX7e2V/7x6XvV4/7DPQ+/Q9cfUdiGwoZfv3Kez7qCeX+n0MT99XHQZbaUCiAzzoSHO6jzpgidLvsw4KofUwKqOU6XxMderUMScWOneUDkeuA2fowCExPR2ABvn2/YzsfXzUcSQs+2erA2REdqEgstexCo7B8fcYrEGLnQ7uogODxOQxSY+NevJ88eJFU34dACi6x6TYpIN/6EUFDe510TLrsUin5ND6E5V54mLi+xGb74d9UJZRo0aZemkfPOthg5KErRsaWD1qtF/9HkVnZEodIOpxRozU75iOwKsDpSgNLvXzA6KCwM1N+kOsQ1PrvCg62p1eqYwteoXzUXRY8ujc54ruj54w6MnuW2+9JaVKlTI/APaJgMuVK2dGRrSPkvW4dISm5557zpys6CiM+u/w9Oqy/UQoule4PMnd99xd9vdDW2S01e1hohoMhhdTn68n30u9IutqBDINqOyBm7YmaPBmnzh78ODBTsGT/epyVOn3QkfY0xHC9HsTncAN7uMYHH+PwTqCn76nWqbff//d7cDN6sf3yN778HSkTP3stO7rSIjasqSjzn7//fcmINA6pBeWfP390GOyTgGidVMvcmoLrWZqPIrOralBW9myZU3gp1MMaIBmv6iqAbmOUBvd71FUjgsPowGttiTabdq06bGeD/ELgVs0DBgwwBxINF1q3bp1Lrexp7HpFXO9quPqipa99SSm5zyKLvtQyHp1y9VVbE0fiSl6RVNbLc6cOWOuOukQxq7Yf5j16puecEf1h8L+nuqPW2RianLgx2G/Yhl+KP7I2N8PTRmLzuTNOgyyLq5a3ezvlb0l6nHZPwN7PXdFr+aH3Ta67NMwROVEQOuRvncauG3bts2kHOk+69Vqd2kwqFwN7R4T9Gq4tnJoaqC+j66+l+4cR+zbaAulBruuWt0e9p2xCo7B8fMYrCffGrRosKIXaqJyEh++THqsjeyYdOXKFfO+hC1/bLIH5Jou7u77o8GDtrrZ03m1tXPo0KHme6GtUo96b33h+6GpkjoVh2aYqM6dOz8yaNKLEHpc0Hqqf8P/1un9mu7uTb169TIX/PTCorbsLlu2TKZMmWIyQoBHoY9bNOhVH/vV+tdff93lNnoiaE8zcHWCrSeZ9vWas20F9h8sPViGp1fz9GQvJuhJqPZv0B/QVq1aOeblcUWvjOkJjP6w2fuDRYUeEJU+xr5fYX399dcR5nLxBnt/uE8++cT8oDyKXkXVH3vtqxHdYGH+/PkR1t29e9ecLIbtrxL+5EL717jD/jz6vK5SZvSqsKZJatqYznnnCTpPXp48ecwJsPYdmzt3rqOPR3SuHttPKPQ5Y4Om2WmZVWSBun1eo6gcR/S4pJOGK1fzA+p3M+yVYKviGBx/j8H29DJ9/KNSSvUzDpsuZz8muWqdD/td0j6InghW7K+h/bdclV1b0NxJI9X5OZXOIfio/lu+8v3o2LGjuYCli70P4sNo8K1zrGkg6uoCpaZZR3aRL7q/de7Q33qdw1RT7nX+PL1YoxfntK90dObkRPxD4BZN2tlVW0u0iTuyiZXtfW7GjBnjlMevBw39odQrLnpgiexKp6fZOxRPnTrVaf3+/fvNla6YoPuuOfj6Y6qTh77//vuPfIz9pEJbS7755huXz6mfww8//OBYp1dldSJQbVXQz0pPVMJOCOuqP5Q36CSymv6jHeEbN27sNHCJ0oAn7I+3Hux1wAIN8rRjuKsJk3Vf9aQmslY8rY+7d+92SsfRFgy98qdX18OnQ9lb4Pbs2ePWvun+6HdE961Pnz5OP4ba0qYd65Xujw6K4Sn2lMhZs2Y5gpfIBv7QSaq1vriaMF3r5eeff26CK3fTLN1hf59mzpzpCBTt9MRKP2tNAdL0wahe7bX3FQtbR/RkR78X7k7y7C0cg+PnMVhbxu3fCR34SPt4hX3usH33dHJw7YNqp5+zntBrS/ubb77pdAL/xx9/OPbzcQYccoe+//aLaWEHGdE+t3pM1nTQ8LQlTT8zV/0c7Z+NtkxG1m/N174fmt6uFyx0Cd//0hX9jdT914sC4S9S6uAmmo4cmej+1kWVfo81ENWLhNpHVwct0+/IO++8Yy6e6r5a4YIyrI1UyWjSKySae60nfHoS50qnTp1M7rkePPSAo1cgtVOr/mjoF1ib/PXEMWyHa2/S3HhNu9DR2vQqUIECBUyrzi+//GJ+hPXKa2QjFUaVnizYr+hrS0tkPwgNGjQwi9IAZfLkyebHWgMd7d+lqYJ61V3TQ/QHR8upP3RhR3DT912vsOpVLU2n0pYL/azWrFljriBr35HITvg8RQ/getVYTzA0QNMTUS2nXl3UDuG6b/rDGTbdSPu+aH6+1p2iRYua/H1tRdEAQoMv/bHVwE6fL3w/N31+bd3SHwt9b/R19ORARxfTtD99zvBBlAZyOqqYjkKo76+9M7ee3Ojn8LDvyNKlS02rogYdmrZSpkwZc+VePwMNSnW/td55krYwaEqRXnlVlSpVcozi6Cpw06BTT6h1X/W90fdePxe9oq37OGfOHClYsGCslVfT2bS8eiKlJ63at1E/Rw269FiSIEECE4Tq9zUq9CR61apV5iRP645eTdfPVE+8dd+6dOliPi936clk2CBAn8t+HLQPxqH90x7VShJVHIPj7zFYgzEdkEMvPmiZ9K8O7qCfrQaKmv5sP2ZqmcKe1OsJs15U0pY7LZ9eONOya/qlXlzS4NRTgYp+l7Xl86uvvjL1U98frZNaPzUw04sx+r6HpccdLZ+OjqjHf/tAK5pFoMGnprROmDDBHBcexde+H1Gh+z18+HAzWqMe66dPn25+H7UVUvdVf8e0LrpKJdXfOs3C0FZ8HcRK3wt9PzX1VPuXPg4d3EfrndZP/Z6H7YOox2TNotHfS30tTZ0EIhXpRAF45DxEOrmjTrDqau6lsHTeqCpVqpiJLHXCyqxZs5q5SVxNnhmVebMeNTdMZPNqhZ9HKvxcZErnXdE5kXRCzuTJk9sKFixo5nDRCWEjm5vEnTmEojKHlS46p0t4u3btsnXs2NHMsaPzmGn5dC4tnaRZ56E5ceJEhMfoRK/6XuvcRIkTJzbbDxgwwMzl8qg5W6Izj1tk7/mjPledw23cuHFmwlqd10znqdLtX3jhBTN3kSvLly83ExLr/Gtar7R+5cuXz0zmqXUu7HxDYcunEy3rZ6pz7ejrpE2b1sy/tGfPnkjr+dixY20FChQw73v4uZcete86QW3Xrl3Ne6+fge5f2bJlzTxiWpbwHjX3VFTe60fRSYwf9b1VOqedlkP3XSda1gmZU6VKZStWrJitX79+ZiJxd7k7AXfYibi13FoOnYdI51tr3LixbdOmTW7PEaXvu84llj9/flMH9Dnr169vJpJ2d9JmV5MUR7a4+5lxDOYY/DA6l59OTK7HTfv3Qid+1u9nz549bdu2bXP5OJ14Xt8TnZfNfuysWrVqpMdae9129Z486vN81Hdd56MbOnSoeV+0LEFBQWby6b/++svl56dz+OlcnA0bNjSfg07ErXPl5cmTx0wuvWXLFrfni4vp78ejXi8q87hFxcPmcdM5UsuVK2f2Sd+jkiVLmjnTdH66h5VfJ/kuXry4qdvhjytR/e1xVa91v+zzj4afGFxdvnzZ1IHI5loF7Pz0f5GHdQDiAr36rFdmte+MLww+AQAAAGf0cQMAAAAAiyNwAwAAAACLI3ADAAAAAIsjcAPiAfsE1fRvAwAAeDw6OqmOuKuj/eroo19++eUjH6Ojh+qo3joqso7OG9n8rA9D4AYAAAAAUaTTLumUOjrlRFTo/LXPPfecmYJHp23SOVVfffVVWblypbiDUSUBAAAAIBq0xU3nKLXPfemKzin53Xffye7dux3rmjZtaiZdX7FiRZRfixY3AAAAAPHanTt35OrVq06LrosJGzZskBo1ajitq127tlnvjoQSDySrNcHbRQCi5dw3/bxdBMBtd+6HeLsIQLSkSBIvTosQByX10aqbrFg3sYoB9QNl1KhRTutGjBghI0eOfOznPn36tGTIkMFpnd7W4PDWrVuSLFmyKD2Pj37MAAAAABAzBg0aJH369HFapwOJWAmBGwAAAIB4LUmSJLEWqGXMmFHOnDnjtE5vp0qVKsqtbYrADQAAAIDn+cWP4TbKli0ry5cvd1q3atUqs94d8ePdAgAAAIAYcP36dTOsvy724f7138eOHXOkXbZq1cqxfefOneXQoUPy+uuvy759+2TGjBny6aefSu/evd16XQI3AAAAAIiiLVu2SLFixcyitG+c/nv48OHm9qlTpxxBnMqZM6eZDkBb2XT+t3feeUfef/99M7KkO+LFPG6MKglfxaiS8EWMKglfxaiS8FU+O6pkiZ5iFbe2Tharo8UNAAAAACyOwA0AAAAALM5HG1YBAAAA+LR4MqpkTOHdAgAAAACLo8UNAAAAgOf5+Xm7BD6FFjcAAAAAsDgCNwAAAACwOFIlAQAAAHgeg5O4hXcLAAAAACyOwA0AAAAALI5USQAAAACex6iSbqHFDQAAAAAsjhY3AAAAAJ7H4CRu4d0CAAAAAIsjcAMAAAAAiyNVEgAAAIDnMTiJW2hxAwAAAACLI3ADAAAAAIsjVRIAAACA5zGqpFt4twAAAADA4gjcAAAAAMDiSJUEAAAA4HmMKukWWtwAAAAAwOJocQMAAADgeQxO4hbeLQAAAACwOAI3AAAAALA4UiUBAAAAeB6Dk7iFFjcAAAAAsDgCNwAAAACwOFIlAQAAAHgeo0q6hXcLAAAAACyOwA0AAAAALI5USQAAAACeR6qkW3i3AAAAAMDiaHEDAAAA4Hn+zOPmDlrcAAAAAMDiCNwAAAAAwOJIlQQAAADgeQxO4hbeLQAAAACwOAI3AAAAALA4UiUBAAAAeJ4fo0q6gxY3AAAAALA4WtwAAAAAeB6Dk7iFdwsAAAAALI7ADQAAAAAsjlRJAAAAAJ7H4CRuocUNAAAAACyOwA0AAAAALI5USQAAAACex6iSbuHdAgAAAACLI3ADAAAAAIsjVRIAAACA5zGqpFtocQMAAAAAi6PFDQAAAIDnMTiJW3i3AAAAAMDiCNwAAAAAwOJIlQQAAADgeQxO4hZa3AAAAADA4gjcAAAAAMDiSJUEAAAA4HmMKukW3i0AAAAAsDgCNwAAAACwOFIlAQAAAHgeo0q6hRY3AAAAALA4WtwAAAAAeB6Dk7iFdwsAAAAALI7ADQAAAAAsznKB27p16+T+/fsR1us6vQ8AAABAHEmVtMriAyxXyqpVq8rFixcjrL9y5Yq5DwAAAADiG8sFbjabTfxcDA164cIFSZEihVfKBAAAAADeZJlRJRs1amT+atDWpk0bSZIkieO+kJAQ2blzp5QrV86LJQQAAAAQY5jHzTcDt9SpUzta3FKmTCnJkiVz3Jc4cWIpU6aMdOjQwYslBAAAAIB4HrjNnTvX/M2RI4f069ePtEgAAAAgLvORQUGswnLv1uuvv+7Ux+3o0aMyadIk+eGHH7xaLgAAAADwFssFbvXr15d58+aZf1++fFmeeeYZeeedd8z6mTNnert4AAAAAOBxlgvctm3bJhUrVjT/Xrp0qWTMmNG0umkwN2XKFG8XL87rVK+Y7JvXUS5921vWTWkhJYMzRrptwgT+MqhFWdnzUQez/aaZraVmyRxO2/RrWlp+nfqKnP2ypxz99DX5dGQDyZ0lwAN7gvjm08UL5flnq0nZkoWlVfMmsnvXzoduv+qHFdLohTpm+yaN6smvv/zsuO/evXsy5d23zfryzxST2tUryvDBA+Tc2TMe2BPEJ58vWSSNnqspVcoUk1dbNZU/dz+83q5ZtVKaNnrebP9Kkway/teI85seOfS3vN6rq9SsVFqqlSsp7V5pIqdPnYzFvUB8tHjRQqlTs5qUKlZIWjRtLLt2Przu/rDye6n//LNm+xcb1JNf1v13zLWPcTB96mSpXrmCPFO8sHRs30aOHj0Sy3sBr9MsO6ssPsBygdvNmzfN4CRK0yN1tEl/f38zOIkGcIg9L1UOlnGdqsgbC9ZL2dfmyc5D5+TrNxtL+jTJXW4/sk0FefW5ItJn+o9S7NUP5f3vdsiSEQ2kSK4gxzYVC2WVWV//IZV7LpDnB35mgr1vxzaW5EkTeXDPENf9sGK5TJzwlnTs3FUWLlkmeYKDpVvnV+XihQsut9+xfZsMGdBXGjR8SRZ9+oVUqVZD+vbsJn8dPGDuv337tuzb+6e82uk1Wbjkc3l74lQ5cuSw9O7xmof3DHHZjyu/lykTx0u7jq/J3EWfydO5g6V3105y8aLrertrxx8yYnB/qVe/kXy0aKlUqlJNBvbpLn//ddCxzT/Hj0nn9i0le46cMu29j2TekmXStkNnSRxmpGbgca34frm8PX6sdHqtqyz+7AsJDs4rXTq1N1M3ubL9j20ysH9fadjoJVmy9EupWq269OreVQ7+e8xVcz+YI58snC9DR4yUBZ98agap69Kxvdy5c8eDewZYm59NL3FYSOHCheXVV1+Vhg0bSsGCBWXFihVStmxZ2bp1qzz33HNy+vRpt58zWa0JsVLWuEZb2LbuPy29p682t/Xiw18LO8vMr7bJ20s2R9j+0CddZNyijTL7mz8c6z4ZVl9u3b0v7cZ95/I1AlMnk+OfdZMafT+R33b9E4t7Ezec+6aft4vgE7SFrUDBgjJg8HBzOzQ0VOrWqiIvN3tF2rbvGGH7gf17y61bN2XytNmOda1bvCzBefPK4GGjXL7Gnt27pFXzxvLtyjWSKdOTsbg3vu/O/RBvF8EnaAtbvvwFpe/AoY5626BOdXmpaXNp1TbiKMrDBvSVW7duydtTZjjWdWjVTHIH55XXh4x4sM3AfpIwYUIZ8b+3PLgncUeKJJYZs83StIWtQMFCMnjof8fcWtUrS7PmLaV9h4jH3P59e5m6O23Gf8fcV5o1McfcYSNGm9a2GlUqSqs2baV12/bm/mvXrkm1SuVk9BtvSZ26z3lw73xTUh+tuskavCdWcevLiHXXaizX4jZ8+HAzqqSOLlm6dGkTtNlb34oVK+bt4sVZiRL6S7HcGWXNH/+1ampIr7efyef6JDVxogRy+959p3UatJUrkDnS10mV4sFV30vXbsdY2RG/3bt3V/bt3SPPlPlvnkdtpX+mdFnZtWO7y8fs3LFdSpd2nheybLnyZn1krl+/ZgZOSpkyVQyWHvG53u7f+6eULP3gN85eb0uVLiO7d+5w+Zjdu7ab+8MqXba87N653XHyvOHXnyVb9uzS67UOUrd6RRMc/rz2wcU4ICbcu3tX9v65R8qUdT7mlilTTnbu+O9Cblg7t2+XMmX+q+uqXPkKZr068c8/cv78OSkd5jiu2VeFCheJ9DkRh0aVtMriAyxXypdeekmOHTsmW7ZsMa1tdtWrV5d3333Xq2WLywJTJTNpjGcv3XRar7czpnU9NcOPWw5Lj0YlJdeTaUzrXLXi2aV++dyRbq/bTOhcTdbv/kf+PHI+VvYD8c/lS5ckJCRE0qVL57Q+XbpAOX/edT27cP68pA23fdp0gWa9K5qqo33eatd5Tp544okYLD3iKx18S+tt2rTh6mHadHLxQuT1NiBcvdXb9vS0SxcvmO4G8+d+IGXKVZBJM96TSlWry+B+PeWPrb/H4t4gPrl0ObJjbrpIj7m6Xo/JEbb/t65r0GbWBUb9OYH4yHINqzqfW9OmTc2gJGHp6JJRoSdY4fOhbaH3xc/fcrvq8/rNXCMzeteWHR+0F823PXTyssz7Ybe0rl3Q5faTutWUAjkCpXqfRR4vKxBdOlDJwH69TAv0oKEjvV0cIFKh//Z8qFilqjR9pbX5d57gfLJ7x3b5YukSKVailJdLCACIUy1uAwcOlAwZMkj79u1l/fr1bj9+7Nixkjp1aqfl/uE1sVLWuOT81VtyPyRUggKcByLR26cv3nD9mCu3pMnILyXdC5Mk+JXZUqT9B3Lj1l05fOpKhG3f7Vpd6pZ5Smq/vkROnL8ea/uB+CdNQIAkSJAgQqf4CxfOS2Cg8xVeu3SBgREGLtFWDl0fIWjr31tOnTopM977gNY2xJg0adKYeht+IBK9ra2/rmj9vBSu3upte8uHec6ECSXHU7mctsme8yk5c/pUjO8D4qeANJEdcy9EeszV9XpMjrD9v3U9MDD9g3Xno/6ciCO8PZKkH6NKPpYTJ07Ixx9/bJrGq1SpInnz5pVx48ZFeVCSQYMGyZUrV5yWhDmrxXq5fd29+6Hyx8HTUrVodsc6rcN6e/Pehw8jfedeiJy8cN2kWjaokEe+3fBXhKDthfK55dn+S+To6YhBHfA4EiVKLHnzFZDfN21wrNO+Pr9v2iiFihR1+ZjCRYrK5jDbq00b15v14YO240ePysz35kqaNExjgZitt8H58svWzRud6u2WzZukYOEiLh9TsFBR2RJme6X1uGDhoo7n1MFOjh1xHkL9+LGjkpEBdRBDEiXWelZANm10PuZu2rRBChdxPRZB4aJFZdNG57q7ccN6s15lzpLFBG/6HHbXr1+XXTt3RPqcQHxkucBNR8PSESW/+uorOX78uHTo0EEWLlwo2bJlkxdeeMGs1wNEZJIkSSKpUqVyWkiTjJopn2+RtnULS4uaBSQ4a1qZ0qOWGbZ/3srd5v73+9eV0e0ezLGnSuXNZPq05ciYWsoXzCxfv/mS+Pv7ycRP/xuBclL3GtK0en5pPfZbuX7rnmQISGGWpIn5TBBzXmnVRr74/DP55qsv5PChv2Xs/0aaEcxeaNDI3K9zsE2d/I5j+2YtWsr69b/K/I8/lMOHD8nsGVPlzz17pEnTFo6gbUDfnrJ3z27531sTJCQ0xPTB0EUHlQBiQtMWreXrL5bK8m++NHOvTXhztNy+dUuef6GhuX/0sEEyc+p/fbubNH9FNm74TRbN/0iOHD4k78+aLvv+3C0vvtzcsU2LVm1l9Q/fy1fLPpN/jh2VpYsXym/rfpJGjZt6ZR8RN7Vs3VaWLf1Uvv7yCzn099/yv9EPjrkNGj445g4Z9LpMfve/Y26LV1rJ+t9+kY8/+tAco2dOnyp7du+Wps1fMffrwE8tWraSObNnyk9rVsvBA/tl6KDXJX1QkFSrXsNr+4nYp5+9VRZfYOmzZ02ZrFChghw4cMAsu3btktatW0tAQIDpC6ctcog5S3/eL4Gpk8vwVuVNcLXz0FmpP2SpnL38YMCSrEEpHX0oVJJECWREmwqSM1MauX7rrqzcfFjaj/tOrty44zSht1r1TjOn1+owYbksWLXHY/uGuK3Ws3Xl0qWLMmvGVLlw/pzp1zN15hxHZ/jTp0+Kn/9/B+UiRYvLG2+9LTOnTpLpU96VbNlyyDuTp8nTufOY+3Wi7Z9/epBi3axxA6fXmv3Bx1KyVGmP7h/iphq168jlSxdlzsxpJlVXh/WfOG22I1VS0xv1YphdoSLFZNQb4+W9GVNk9rRJkiVbdnlr4lTJ9XRuxzaVq9WQ1wePkHlz58i7E8ZK9uw55I0Jk6RIsRJe2UfETc/WqSuXLl6UGdOmmAtawXnzyYzZ7zvSzU+fOiX+YUbpK1qsuIwd/7ZMmzJJpk6aKNmy55BJU6dL7n+Puapt+w4m+Bs9crhcu3ZVihUvYZ5TL8gDsOg8burMmTMyf/58E5wdOnRIGjRoYPq81ahRQ27cuCGjR4+WxYsXR3lCbuZxg69iHjf4IuZxg69iHjf4Kl+dxy35ix+KVdz8vJ1YneU+5nr16snKlSslT548Jk2yVatWkjZtWsf9KVKkkL59+8qECQRjAAAAgK/ylRRFq7Bc4BYUFCQ///yzY+JtV9KnTy+HDx/2aLkAAAAAQOL74CRr1qyR/Pnzm0m2wwdtOjJkgQIF5JdffnFE59mz/zf6IQAAAADEZZYJ3CZNmmRSI3UUyPB0LrZOnTrJxIkTvVI2AAAAADHMz0KLD7BM4LZjxw559tlnI72/Vq1asnXrVo+WCQAAAACswN9KI0kmSpToofO7nTt3zqNlAgAAAAArsEzgljlzZtm9+8FEz67s3LlTMmXK5NEyAQAAAIgd3p5028/HJuC2TOBWt25dGTZsmNy+fTvCfToh44gRI+T555/3StkAAAAAwJssMx3A0KFDZdmyZWb+tm7duklwcLBZv2/fPpk+fbqEhITIkCFDvF1MAAAAADHAV1q6rMIygVuGDBlk/fr10qVLFxk0aJDYbDbHB1q7dm0TvOk2AAAAABDfWCZwUzo32/Lly+XSpUvy119/meAtd+7cEhAQ4O2iAQAAAIDXWCpws9NArVSpUt4uBgAAAIBYQqqkjw5OAgAAAABwjcANAAAAACzOkqmSAAAAAOI2UiXdQ4sbAAAAAFgcLW4AAAAAPI8GN7fQ4gYAAAAAFkfgBgAAAAAWR6okAAAAAI9jcBL30OIGAAAAABZH4AYAAAAAFkeqJAAAAACPI1XSPbS4AQAAAIDFEbgBAAAAgMWRKgkAAADA40iVdA8tbgAAAABgcbS4AQAAAPA4WtzcQ4sbAAAAAFgcgRsAAAAAWBypkgAAAAA8j0xJt9DiBgAAAAAWR+AGAAAAABZHqiQAAAAAj2NUSffQ4gYAAAAAFkfgBgAAAAAWR6okAAAAAI8jVdI9tLgBAAAAgMXR4gYAAADA42hxcw8tbgAAAABgcQRuAAAAAGBxpEoCAAAA8DwyJd1CixsAAAAAWByBGwAAAAC4afr06ZIjRw5JmjSplC5dWjZv3vzQ7SdNmiTBwcGSLFkyyZo1q/Tu3Vtu374d5dcjVRIAAACAx/nyqJJLliyRPn36yKxZs0zQpkFZ7dq1Zf/+/RIUFBRh+0WLFsnAgQPlww8/lHLlysmBAwekTZs25j2YOHFilF6TFjcAAAAAcIMGWx06dJC2bdtK/vz5TQCXPHlyE5i5sn79eilfvrw0b97ctNLVqlVLmjVr9shWurAI3AAAAAB4nLY2WWW5c+eOXL161WnRda7cvXtXtm7dKjVq1HCs8/f3N7c3bNjg8jHayqaPsQdqhw4dkuXLl0vdunWj/H4RuAEAAACI18aOHSupU6d2WnSdK+fPn5eQkBDJkCGD03q9ffr0aZeP0Za20aNHS4UKFSRRokSSK1cuqVKligwePDjKZSRwAwAAABCvDRo0SK5cueK06LqY8tNPP8mbb74pM2bMkG3btsmyZcvku+++kzFjxkT5ORicBAAAAEC8HpwkSZIkZomKwMBASZAggZw5c8Zpvd7OmDGjy8cMGzZMWrZsKa+++qq5XahQIblx44Z07NhRhgwZYlItH4UWNwAAAACIosSJE0uJEiVk9erVjnWhoaHmdtmyZV0+5ubNmxGCMw3+lM1mi9Lr0uIGAAAAAG7QqQBat24tJUuWlGeeecZMB6AtaDrKpGrVqpVkzpzZ0U+uXr16ZiTKYsWKmekD/vrrL9MKp+vtAdyjELgBAAAAiNepku56+eWX5dy5czJ8+HAzIEnRokVlxYoVjgFLjh075tTCNnToULO/+vfEiROSPn16E7S98cYbUX5NP1tU2+Z8WLJaE7xdBCBazn3Tz9tFANx2536It4sAREuKJFzPhm9K6qNV98lOy8QqTs5uJFZHHzcAAAAAsDgfjc8BAAAA+DTfzZT0ClrcAAAAAMDiaHEDAAAA4HG+PDiJN9DiBgAAAAAWR+AGAAAAABZHqiQAAAAAjyNV0j20uAEAAACAxRG4AQAAAIDFkSoJAAAAwONIlXQPLW4AAAAAYHEEbgAAAABgcaRKAgAAAPA8MiXdQosbAAAAAFgcLW4AAAAAPI7BSdxDixsAAAAAWByBGwAAAABYHKmSAAAAADyOVEn30OIGAAAAABZH4AYAAAAAFkeqJAAAAACPI1XSPbS4AQAAAIDF0eIGAAAAwONocXMPLW4AAAAAYHEEbgAAAABgcaRKAgAAAPA8MiXdQosbAAAAAFgcgRsAAAAAWFy8SJW8tLy/t4sAREtAqW7eLgLgtku/T/N2EQAAPoBRJd1DixsAAAAAWByBGwAAAABYXLxIlQQAAABgLaRKuocWNwAAAACwOFrcAAAAAHgcDW7uocUNAAAAACyOwA0AAAAALI5USQAAAAAex+Ak7qHFDQAAAAAsjsANAAAAACyOVEkAAAAAHkempHtocQMAAAAAiyNwAwAAAACLI1USAAAAgMcxqqR7aHEDAAAAAIujxQ0AAACAx9Hg5h5a3AAAAADA4gjcAAAAAMDiSJUEAAAA4HH+/uRKuoMWNwAAAACwOAI3AAAAALA4UiUBAAAAeByjSrqHFjcAAAAAsDha3AAAAAB4nB9Nbm6hxQ0AAAAALI7ADQAAAAAsjlRJAAAAAB5HpqR7aHEDAAAAAIsjcAMAAAAAiyNVEgAAAIDHMaqke2hxAwAAAACLI3ADAAAAAIsjVRIAAACAx5Eq6R5a3AAAAADA4mhxAwAAAOBxNLi5hxY3AAAAALA4AjcAAAAAsDhSJQEAAAB4HIOTuIcWNwAAAACwOAI3AAAAALA4UiUBAAAAeByZku6hxQ0AAAAALI7ADQAAAAAsjlRJAAAAAB7HqJLuocUNAAAAACyOFjcAAAAAHkeDm3tocQMAAAAAiyNwAwAAAACLI1USAAAAgMcxOIl7aHEDAAAAAIsjcAMAAAAAiyNVEgAAAIDHkSnpHlrcAAAAAMDiaHEDAAAA4HEMTuIeWtwAAAAAwOII3AAAAADA4kiVBAAAAOBxZEq6hxY3AAAAALA4AjcAAAAAsDhSJQEAAAB4HKNKuocWNwAAAACwOAI3AAAAALA4UiUBAAAAeByZku6hxQ0AAAAALM4yLW5Xr16N8rapUqWK1bIAAAAAiF0MTuKjgVuaNGke+eHZbDazTUhIiMfKBQAAAADeZpnAbe3atd4uAgAAAABYkmUCt8qVK3u7CAAAAAA8hExJHw3cXLl586YcO3ZM7t6967S+cOHCXisTAAAAAHiaJQO3c+fOSdu2beX77793eT993AAAAADEJ5acDqBXr15y+fJl2bRpkyRLlkxWrFghH3/8seTOnVu+/vprbxcPAAAAwGPSQQetsvgCS7a4rVmzRr766ispWbKk+Pv7S/bs2aVmzZpmGoCxY8fKc8895+0iAgAAAED8bnG7ceOGBAUFmX8HBASY1ElVqFAh2bZtm5dLBwAAAACeZcnALTg4WPbv32/+XaRIEZk9e7acOHFCZs2aJZkyZfJ28QAAAAA8Jm+nR/qRKvn4evbsKadOnTL/HjFihDz77LOycOFCSZw4sXz00UfeLh4AAAAAeJQlA7dXXnnF8e8SJUrI0aNHZd++fZItWzYJDAz0atkAAAAAPD4faeiyDEsGbuElT55cihcv7u1iAAAAAIBXWDJws9lssnTpUlm7dq2cPXtWQkNDne5ftmyZ18oGAAAAAJ6W0KrzuOmAJFWrVpUMGTL4TIdBAAAAAFHDOX4cCNzmz59vWtXq1q3r7aIAAAAAgNdZcjqA1KlTy1NPPeXtYsRLixctlDo1q0mpYoWkRdPGsmvnzodu/8PK76X+88+a7V9sUE9+WfdzhLTX6VMnS/XKFeSZ4oWlY/s2cvTokVjeC8Qn5YvnkqWTOsmhH96QW39Mk3pVCj/yMRVL5Jb1iwbI5U3vyu6vRsgr9UpH2KZTk0qy77tRcmnju7JuXj8pWSB7LO0B4jOOufBV1F3A8ywZuI0cOVJGjRolt27d8nZR4pUV3y+Xt8ePlU6vdZXFn30hwcF5pUun9nLhwgWX22//Y5sM7N9XGjZ6SZYs/VKqVqsuvbp3lYMHDzi2mfvBHPlk4XwZOmKkLPjkU0mWLJl06dhe7ty548E9Q1yWIlkS2XXghPQauyRK22d/Mp18MbWzrNtyQEo3fUumLVorM4c3lxpl8zm2ealWcRnXt6G8Mft7Kdt8nOw8cEK+ntFV0gc8EYt7gviGYy58FXUXMUUzJa2y+AJLBm5NmjSRS5cuSVBQkBQqVMiMKBl2QeyY//FcafRSE2nQ8EXJ9fTTMnTEKEmaNKl8uexzl9svXDBPylWoKG3avSpP5col3Xr0knz588viRQscV88Wzp8nHTp1karVakie4Lzyv7Hj5dzZs7Jm9Y8e3jvEVT/89qeMmvGtfL324Vd77Tq8VEGOnLggAyd+IfsPn5FZS9bJF6u3S/cWVR3b9Hilmsxdtl7mf71R9h06Ld3fWCy3bt+V1g3KxuKeIL7hmAtfRd0FvMOSgVvr1q1l69atZj63F198UerXr++0IObdu3tX9v65R8qULedY5+/vL2XKlJOdO/5w+Zid27dLmTLOJ7Llylcw69WJf/6R8+fPSeky/z1nypQppVDhIpE+JxDbShfJKWs37Xdat2r9XildOKf5d6KECaRYvqyyJsw2elKht5/5dxvgcXHMha+i7gLeY8nBSb777jtZuXKlVKhQwdtFiTcuXb4kISEhki5dOqf1evvw4UMuH3P+/HlJly4wwvbnL5z/9/5zD9YFRnxOfSzgDRnSpZIzF685rTt78aqkTplMkiZJJAGpkkvChAnkbPhtLlyV4BwZPFxaxFUcc+GrqLuISYwqGQcCt6xZs0qqVKmi9VjNhQ6fD21LkESSJEkSQ6UDAAAAAM+yZKrkO++8I6+//rocOeL+aEJjx441o1KGXSaMGxsr5YxLAtIESIIECSJ0LNbbgYHOV8nsdP2Ff6+WOW3/71W1wMD0D9adj/pzArHtzIWrkiFtSqd1QWlTyZVrt+T2nXty/tJ1uX8/RILCb5MulZy+cNXDpUVcxTEXvoq6i5jk7QFJ/Bic5PFp37a1a9dKrly5TI5z2rRpnZaHGTRokFy5csVp6T9gkMfK7qsSJU4s+fIXkE0bNzjWhYaGyqZNG6RwkWIuH1O4aFHZtHGj07qNG9ab9SpzlizmYKzPYXf9+nXZtXNHpM8JxLZNOw5LlWeCndZVL5NXNu08bP59736I/LH3uFQtHeyUylH1mTyy+d9tgMfFMRe+iroLeI8lUyUnTZoU7cdqSmT4tMjb92OgUPFAy9ZtZdjgAVKgQEEpWKiwLJj/sZmSoUHDRub+IYNel6CgDNKzd19zu8UrraR9m5by8UcfSqVKlc3wwHt275ZhI0c7TnZbtGwlc2bPlOzZspsDs87Rkj4oSKpVr+HVfUXckSJZYsmV9cHVWpUjczopnCezXLp6U46fviSju78gTwallleHzTf3z1n6q3RuWkne6FlfPv5qo1QplUderFlMGvaY5XiOKQvWyJzRLWXrn8dky+4j0q15VUmeLInM+8r5xAN4HBxz4auou4B3WC5wu3fvnvz8888ybNgwyZmTEdw86dk6deXSxYsyY9oU01E4OG8+mTH7fUn3b5rC6VOnxN/vv0baosWKy9jxb8u0KZNk6qSJki17Dpk0dbrkzp3HsU3b9h3MwXz0yOFy7dpVKVa8hHlO+hwiphTPn11+eL+n4/b4fi+avzqUf8cRCyRjYCrJmvG/lvqjJy9Iw+6zZHy/RtK1eRU5ceaydBm9SH7csNexzdIftklgwBMyvMtzkiFdStm5/4TU7zo9woAlwOPgmAtfRd1FTPH3lRxFi/Cz6TjXFqP90rZv3x5jgRstbvBVAaW6ebsIgNsu/T7N20UAgHglqeWaYqKm5jTrZLKs6lZGrM6SfdwaNGggX375pbeLAQAAAACWYMn4PHfu3DJ69Gj57bffpESJEpIiRQqn+3v06OG1sgEAAAB4fGRKxoHA7YMPPpA0adLI1q1bzRKWdmAlcAMAAAAQn1gycDt8mCG3AQAAgLhMG2Tg433cwtKxUyw4fgoAAAAAeIxlA7d58+ZJoUKFJFmyZGYpXLiwzJ//YB4mAAAAAIhPLJkqOXHiRDOPW7du3aR8+fJm3a+//iqdO3eW8+fPS+/evb1dRAAAAACPwZ9MSd8P3KZOnSozZ86UVq1aOda98MILUqBAARk5ciSBGwAAAIB4xZKpkqdOnZJy5cpFWK/r9D4AAAAA8Kbp06dLjhw5JGnSpFK6dGnZvHnzQ7e/fPmydO3aVTJlyiRJkiSRPHnyyPLly307cHv66afl008/jbB+yZIlZo43AAAAAL4/qqRVFndpXNKnTx8ZMWKEbNu2TYoUKSK1a9eWs2fPutz+7t27UrNmTTly5IgsXbpU9u/fL3PmzJHMmTP7dqrkqFGj5OWXX5Z169Y5+rjpZNyrV692GdABAAAAgCfH5OjQoYO0bdvW3J41a5Z899138uGHH8rAgQMjbK/rL168KOvXr5dEiRKZddpa5w5Ltri9+OKLsmnTJkmXLp18+eWXZgkMDDTNjw0bNvR28QAAAADEU3fv3pWtW7dKjRo1HOv8/f3N7Q0bNrh8zNdffy1ly5Y1qZIZMmSQggULyptvvikhISG+3eKmSpQoIQsXLvR2MQAAAADEAivNv33nzh2zhKX90HQJT0e514BLA7Cw9Pa+fftcPv+hQ4dkzZo10qJFC9Ov7a+//pLXXntN7t27Z9Itfa7FTSPVBAkSPHRJmNCysSYAAAAAHzR27FhJnTq106LrYkpoaKgEBQXJe++9ZxqotFvYkCFDTIplVFkqCvriiy8ivU+bHadMmWJ2GgAAAIBv8xPrNLkNGjTIDDYSlqvWNqVduLRB6cyZM07r9XbGjBldPkZHktS+bfo4u3z58snp06dN6mXixIl9K3CrX79+hHU64op28Pvmm29M0+Lo0aO9UjYAAAAAcVOSSNIiXdEgS1vNdODEBg0amHXauKS3u3Xr5vIxOuDiokWLzHaaZagOHDhgArqoBG2WS5UM6+TJk2aklkKFCsn9+/dl+/bt8vHHH0v27Nm9XTQAAAAA8VifPn3McP4an+zdu1e6dOkiN27ccIwy2apVK9OKZ6f366iSPXv2NAGbjkCpg5PoYCVRZakWN3XlyhWzE1OnTpWiRYuayLVixYreLhYAAACAGORvnUxJt2kftXPnzsnw4cNNuqPGLStWrHAMWHLs2DFHy5rKmjWrrFy5Unr37i2FCxc287dpEDdgwIAov6afzWaziUWMHz9exo0bZ3JDNXhzlToZHbfvx8jTAB4XUMp1cztgZZd+n+btIgBAvJLUck0xUfPCe7+LVXzdsZRYnaU+Zu3LlixZMnn66adNs6MurixbtszjZQMAAAAAb7FU4Ka5oH5WmtABAAAAQKzgvN+HA7ePPvrI20UAAAAAAMux7KiSAAAAAAALtrgBAAAAiB/IlHQPLW4AAAAAYHG0uAEAAADwOH+a3NxCixsAAAAAWByBGwAAAABYHKmSAAAAADyOTEn30OIGAAAAABZH4AYAAAAAFkeqJAAAAACP8yNX0i20uAEAAACAxdHiBgAAAMDjaHBzDy1uAAAAAGBxBG4AAAAAYHGkSgIAAADwOH9yJd1CixsAAAAAWByBGwAAAABYHKmSAAAAADyOREn30OIGAAAAABZH4AYAAAAAcSFVMmfOnOLn5qgvuv3ff/8d3XIBAAAAiMPcjS/iuygFbpUrV+aNBQAAAAArB24fffRR7JcEAAAAQLzhT7uQW+jjBgAAAABxNXC7evWqvPXWW1K7dm0pVqyYbN682ay/ePGiTJw4Uf7666+YLCcAAAAAxFvRmsftn3/+Mf3ejh8/Lrlz55Z9+/bJ9evXzX1p06aV2bNny9GjR2Xy5MkxXV4AAAAAcQBjaHggcOvfv79cu3ZNtm/fLkFBQWYJq0GDBvLtt99G56kBAAAAADGRKvnDDz9Ijx49JH/+/C4j5aeeesq0xgEAAAAAvNTiduvWLUmfPn2k92trHAAAAABEhkxJD7S4aUvbunXrIr3/yy+/NAOWAAAAAAC8FLj16tVLFi9eLOPGjZMrV66YdaGhoWYkyZYtW8qGDRukd+/eMVA8AAAAAEC0UiVfeeUVM2rk0KFDZciQIWbds88+KzabTfz9/eXNN980A5QAAAAAgCuMKumBwE1pwKata59//rlpadMWt1y5ckmjRo3M4CQAAAAAAC8HbipbtmykRAIAAABwmz8Nbp4L3Hbv3i3Lly+XI0eOmNs5c+Y0KZOFChV6nKcFAAAAADxu4Hbnzh3p1KmTzJ8/39GvTWm65MCBA6VFixby/vvvS+LEiaPz9AAAAACAxx1VcsCAATJv3jzp0qWL7N27V27fvm2COf13586dZcGCBfL6669H56kBAAAAxJPBSayyxNkWNw3MdGCSadOmOa0PDg6W6dOny9WrV802kyZNiqlyAgAAAEC8Fa0Wt3v37kmZMmUivb9cuXJy//79xykXAAAAAOBxArfatWvLypUrI71/xYoVUqtWreg8NQAAAIB4wM9CS5xJlbx48aLT7TFjxkiTJk3MnG1du3aVp59+2qw/ePCgSZXUybmXLFkSOyUGAAAAgHgmSoFbYGBghE57Oprkrl275KuvvoqwXhUoUIB0SQAAAAAu+fvIoCA+FbgNHz7cZ0ZbAQAAAIB4GbiNHDky9ksCAAAAAIi56QAAAAAA4HGQ0OfBwO23336Tbdu2yZUrVyQ0NNTpPk2tHDZs2OM8PQAAAAAguoGbjjL53HPPyebNm81gJBqk2Qclsf+bwA0AAAAAvDiPW//+/WXnzp2yaNEiOXTokAnUdF63AwcOSOfOnaVo0aJy8uTJGCoiAAAAgLhGG3qsssTZwG358uXSqVMnefnllyVlypQPnsjf38znpvO45ciRQ3r16hXTZQUAAACAeClagdvly5fNPG3qiSeeMH+vX7/uuL9WrVqmBQ4AAAAA4KXA7cknn5TTp0+bfydJkkSCgoJkx44djvtPnDjhM02OAAAAADxPwwWrLHF2cJJKlSrJqlWrZMiQIea2pkyOHz9eEiRIYEaXnDRpktSuXTumywoAAAAA8VK0Arc+ffqYwO3OnTumxU0n6N6zZ49jFEkN7KZMmRLTZQUAAAAQR/j7SlOXLwduhQoVMotdQECA/Pjjj6bvm7a62QcsAQAAAAB4qY9bZNKkSWOCNp0mQAcoAQAAAAB4qcXtUQ4fPiyrV6+OjacGAAAAEAeQKenFFjcAAAAAQMwjcAMAAACA+JgqCQAAAAAPw7zP7qHFDQAAAADiSotb4cKFo/ykZ8+ejW55AAAAAADRDdzSpk0b5ebMdOnSSb58+aL61AAicWHzVG8XAXBbQJne3i4CEC2XNr7r7SIA8Qqpf7EUuP30009uPjUAAAAAICYwOAkAAAAAj2NwEvfQQgkAAAAAFkfgBgAAAAAWR6okAAAAAI/zJ1PSLbS4AQAAAIDFEbgBAAAAQFxOlTxx4oSsW7fOTLj94osvSpYsWSQkJESuXLkiqVOnlgQJEsRcSQEAAADEGaRKeqDFzWazSZ8+fSRnzpzSokUL8+8DBw6Y+65fvy45cuSQqVOZOBgAAAAAvBa4TZgwQSZPniz9+vWTVatWmUDOTlvaGjVqJJ9//nmMFBAAAABA3JzHzSpLnA3c5syZI61atZI333xTihYtGuH+woULO1rgAAAAAABeCNyOHz8u5cqVi/T+FClSyNWrVx+nXAAAAACAxxmcJCgoyARvkdm6datky5YtOk8NAAAAIB5gcBIPtLhpH7ZZs2bJoUOHHOvsuaE//PCDfPTRR9K4cePoPDUAAAAAICYCt1GjRkmmTJlM/zbt66ZB27hx46RChQpSp04d08dt8ODB0XlqAAAAAEBMBG46cuTGjRvl9ddfN3O5JU2aVH7++We5fPmyjBgxQn755RdJnjx5dJ4aAAAAQDygCXtWWeL0BNzJkiWToUOHmgUAAAAAYLEWNwAAAACAxVvc2rVr98httN/bBx98EJ2nBwAAABDH+ftKjqIvB25r1qyJMMN4SEiInDp1yvxNnz69mcsNAAAAAOClwO3IkSMu19+7d09mz54tkyZNklWrVj1u2QAAAADEUfTZ8uL7lShRIunWrZvUqlXL/AUAAAAAWDTQLVKkiKxbty42nhoAAAAA4p1oTwfwMJomyTxuAAAAACLD2CQeCNxGjx7tcr1OwK0tbdu2bZOBAwdG56kBAAAAADERuI0cOdLl+oCAAMmVK5fMmjVLOnToEJ2nBgAAAADEROAWGhoanYcBAAAAgME8brE8OMmtW7ekT58+8s0337j7UAAAAACAJwK3ZMmSmbnazpw5E53XAwAAAAB4IlWyRIkSsnv37ug8FAAAAAAYVdIT87hNmjRJFi9eLO+//77cv38/Ok8BAAAAAIjpFjcd5j9fvnySPn16ad26tfj7+0unTp2kR48ekjlzZpNCGZafn5/s2LEjqk8PAAAAIB7xp8UtdgK3qlWryoIFC6RZs2aSLl06CQwMlODgYPdeDQAAAAAQe4GbzWYzi/rpp5/cfyUAAAAAgOcGJwEAAACAx8E8brE4OIn2WwMAAAAAWDhwe+WVVyRBggRRWhImpDEPAAAAAGKCW9FVjRo1JE+ePDHywgAAAADiL5L5YjFw02kAmjdv7uZLAAAAAAAeB/mMAAAAADyOedxisY8bAAAAAMDzCNwAAAAAIK6kSoaGhsZuSQAAAADEG35CrqQ7aHEDAAAAAIsjcAMAAAAAi2NUSQAAAAAex6iS7qHFDQAAAAAsjsANAAAAACyOVEkAAAAAHkeqpHtocQMAAAAAi6PFDQAAAIDH+fnR5OYOWtwAAAAAwOII3AAAAADA4kiVBAAAAOBxDE7iHlrcAAAAAMDiCNwAAAAAwOJIlQQAAADgcQwq6R5a3AAAAADA4gjcAAAAAMDiSJUEAAAA4HH+5Eq6hRY3AAAAALA4WtwAAAAAeBzzuLmHFjcAAAAAsDgCNwAAAACwOAI3AAAAAB6nY5NYZYmO6dOnS44cOSRp0qRSunRp2bx5c5Qet3jxYvHz85MGDRq49XoEbgAAAADghiVLlkifPn1kxIgRsm3bNilSpIjUrl1bzp49+9DHHTlyRPr16ycVK1YUdxG4AQAAAIAbJk6cKB06dJC2bdtK/vz5ZdasWZI8eXL58MMPI31MSEiItGjRQkaNGiVPPfWUuIvADQAAAIDH+YufZZY7d+7I1atXnRZd58rdu3dl69atUqNGjf/2xd/f3N6wYUOk+zt69GgJCgqS9u3bR/P9sqi//vpLVq5cKbdu3TK3bTabt4sEAAAAIA4aO3aspE6d2mnRda6cP3/etJ5lyJDBab3ePn36tMvH/Prrr/LBBx/InDlz4s48bhcuXJCXX35Z1qxZYzrtHTx40DQlamQaEBAg77zzjreLCAAAAOAxRXdQkNgwaNAg02ctrCRJksTIc1+7dk1atmxpgrbAwMC4E7j17t1bEiZMKMeOHZN8+fI51mswp28mgRsAAACAmKRBWlQDNQ2+EiRIIGfOnHFar7czZswYYfu///7bDEpSr149x7rQ0FDzV+Oe/fv3S65cuXwvcPvhhx9MimSWLFmc1ufOnVuOHj3qtXIBAAAAQOLEiaVEiRKyevVqx5D+Gojp7W7dukXYPm/evLJr1y6ndUOHDjUtcZMnT5asWbNG6XUtF7jduHHDjMgS3sWLF2OsuRIAAACAd/lbKFXSXZoJ2Lp1aylZsqQ888wzMmnSJBPH6CiTqlWrVpI5c2bTT07neStYsKDT49OkSWP+hl/vU4Gbzmkwb948GTNmjLmt/dw0gh0/frxUrVrV28UDAAAAEM+9/PLLcu7cORk+fLgZkKRo0aKyYsUKx4Al2u1LR5qMSX42iw3XuHv3bqlevboUL17cDFDywgsvyJ49e0yL22+//Ral/M/wbt+PlaICsS7UWl9PIErSlXXu3A34iksb3/V2EYBoSWq5ppiombXhiFhF57I5xOos9zFrc+GBAwdk2rRpkjJlSrl+/bo0atRIunbtKpkyZfJ28QAAAADEAH8rDSvpAywXuCmdN2HIkCHeLgYAAAAAWIIlA7fLly/L5s2b5ezZs46hMu20ox8AAAAAxCeWC9y++eYbadGihUmRTJUqlRmcxE7/TeAGAAAA+D4yJd0Ts0OdxIC+fftKu3btTOCmLW+XLl1yLDpACQAAAADEN5YL3E6cOCE9evRwOZcbYt/iRQulTs1qUqpYIWnRtLHs2rnzodv/sPJ7qf/8s2b7FxvUk1/W/ex0vw5aOn3qZKleuYI8U7ywdGzfRo4etc4IQog7lnyyUOrWqialixeWls2ayO5dD6+7q1aukIb16pjtGzeMWHdXr/pBunRoJ1XKl5ZiBfPK/n17Y3kPEB91alxe9n09TC79Nl7WfdRLShbIFum2CRP4y6BXa8meL4eY7Tct6ic1y+Z12uaJ5ElkQp8Gsv+bYXLx13Gy9oMeUiJ/1CZ2BdzB+QJianASqyy+wHKBW+3atWXLli3eLka8tOL75fL2+LHS6bWusvizLyQ4OK906dReLly44HL77X9sk4H9+0rDRi/JkqVfStVq1aVX965y8OABxzZzP5gjnyycL0NHjJQFn3wqyZIlky4d28udO3c8uGeI61Z+v1zeGf+WdOrSVRZ9tkzyBAfLa51elYsPqbuDXu8rDRq+JJ989oVUqVZD+vToJn+Fqbu3bt2SosVLSI/e/Ty4J4hPXqpZVMb1biBvzFkpZV95R3YeOClfT+0k6QOecLn9yNfqyquNykqfCcukWJNx8v7n62XJhLZSJDizY5uZQ1+WaqWDpd3whVKy6QT5cdN++W5GF3kyfWoP7hniOs4XAO+w3DxuH3zwgYwePdrMOl6oUCFJlCiR0/06r5u7mMctavSKWYGChWTw0OHmtg4MU6t6ZWnWvKW079Axwvb9+/YyJ7fTZsx2rHulWRMJzptXho0Yba6e1ahSUVq1aSut27Y391+7dk2qVSono994S+rUfc6De+ebmMctarSFrUDBgjJwyH9199kaVaRp81ek3asR6+6Avr3l1q2bMiVM3W3V/GXJE5xXho4Y5bTtyRP/yHO1a8jipV9IcN58Htgb38c8blGjLWxb/zwmvccvc/Tj/uu74TJzya/y9serI2x/6PuRMu7DVTL7s98c6z4Z30Zu3b5nArWkSRLJuZ/HSuO+H8qK3/50bPPb/D7yw/q9Mmrm9x7aM9/FPG5Rw/mC9fjqPG4fbD4mVtH+mcgzHqzCci1uHTp0kOPHj5vgrXHjxtKgQQPH0rBhQ28XL866d/eu7P1zj5QpW86xTmd7L1OmnOzc8YfLx+zcvl3KlCnrtK5c+QpmvTrxzz9y/vw5KV3mv+fUufkKFS4S6XMC7rp370HdDVvPtO6WLlNWdu54UBfD0/Wlw9R1VbZc+Ui3B2JaooQJpFjeLLJm038tDnryumbzQXmmcHaXj0mcKKHcvut8JVKDtnJFn3KkUiZMmEBu373ntM3tO/9tAzwuzhcQkzRD0SqLL7Bc4KZXbSJbQkJCvF28OOvS5Uvm/U2XLp3Ter19/vx5l4/R9enSBUbc/sKD7fUgbNYFRv05AXfpwEVad9NGqLuBcuEhdTfC9oGRbw/EtMA0KUyQdfbiNaf1ejtjulQuH/Pjxn3So3kVyZU10LTOVSudR+pXKywZAx9sf/3mHdm447DpB5cpMJX4+/tJ0zolpHShHI5tgMfF+QLgPZYL3B6X5kJfvXrVaSE/GgDg6/q9/YX8ffyc7Fg6SK5umCDvvv6izPt6s9N8p5oyqReOD60YJVfWT5CuTSvKpyu3SWgoadcA4OssGbj9/PPPUq9ePXn66afNov3afvnllyg9duzYsZI6dWqnZcK4sbFeZl8XkCZAEiRIEKFjsd4ODHS+Sman6y/8e7XMaft/r6oFBqZ/sO581J8TcFdAwIO6G34gEq2b2ormita/CNvrFWHqJTzk/OUbcv9+iASlTem0Xm+fvnA10sc06fehpKs4QILrjZEiL46VG7fuyOET/02Vc/jEBanVabqkqzBAcj83Wiq2nmTSMnU9EBM4X0BMByJWWXyB5cq5YMECqVGjhpkOQKcF0EVHFqpevbosWrTokY8fNGiQXLlyxWnpP2CQR8ruyxIlTiz58heQTRs3ONbpVdxNmzZI4SLFXD6mcNGismnjRqd1GzesN+tV5ixZzMFYn8NO5+fbtXNHpM8JuCtRon/r7ibnurt500YpXORBXQxP128OU9cddTeS7YGYdu9+iPyx7x+p+kwexzpNf6xaKrds3nn0oY+9c/e+nDx3xfRpa1CtsHz7864I29y8fdcEgGlSJpMaZfPKtz/vjpX9QPzD+QLgPZYbg+aNN96Q8ePHS+/evR3rNHibOHGijBkzRpo3b/7QxydJksQsYTGqZNS0bN1Whg0eIAUKFJSChQrLgvkfm1GgGjRsZO4fMuh1CQrKID179zW3W7zSStq3aSkff/ShVKpU2QwPvGf3bhk2crTjJKRFy1YyZ/ZMyZ4tuzkw6xwt6YOCpFr1Gl7dV8Qtr7RqI8OHDJT8WncLFpZFCx7U3foNHtTdoYMGSFBQkPT4t+42e6WldGjbSuZ99KFUrFRFVn7/nfy5Z4+j7qorVy7L6VOn5OzZs+b2kcOHzV9tlbNfHQYex5SFP8mckc1l65/HZcueo9KteWVJniyxzPtmk7n//VHN5eTZKzJ8+nfmdqkC2eTJoNSy48BJyZw+tQzpWFv8/fxl4rw1juesUSbYHHsPHD1r+sK92eMFOXDkjMz7+sFzAjGB8wXAOywXuB06dMikSYan6ZKDBw/2Spnii2fr1JVLFy/KjGlTTEdhHfp8xuz3HeljehKrJwl2RYsVl7Hj35ZpUybJ1EkTJVv2HDJp6nTJnfu/K8ht23cwB/PRI4fLtWtXpVjxEuY5wwfXwOOorXX30kWZOW2qXPi37k6fNSdM3T1pBmoIW3ffHPe2TJ86SaZNftfU3YlTpsnTYeruz2vXyIih/x1zBvZ/MMS9zhXXuWt3j+4f4qalq7ZLYMATMrzzs5IhXSrZeeCE1O8+W85evG7uz5oxwKlvWpIkiWREl7qSM3M6uX7rjqz8ba+0H75Qrly/7dgm9RPJZHS35yRzUBq5ePWmfLVmh4yYvlzuh/zXDw54XJwvIKZo0A4fnsdN+7T1799fOnXq5LR+1qxZ8s4778jBgwfdfk5a3OCrmMcNvoh53OCrmMcNvspX53H7eMtxsYrWJbOK1VnuY+7bt69Jjdy+fbuUK/dgPo/ffvtNPvroI5k8ebK3iwcAAAAgBtDe5uOBW5cuXSRjxoymde3TTz816/LlyydLliyR+vXre7t4AAAAAOBxlgvcVMOGDc0CAAAAALDgdABPPfVUhLlB1OXLl819AAAAAHyfv5+fZRZfYLnA7ciRIxISEhJh/Z07d+TEiRNeKRMAAAAAeJNlUiW//vprx79XrlwpqVOndtzWQG716tWSI0cOL5UOAAAAALzHMoFbgwYNHPM5tG7d2um+RIkSmaBNBywBAAAA4Pt8I0HROiwTuIWGPpgcNGfOnPL7779L4L+TOAIAAABAfGeZPm4bNmyQb7/9Vg4fPuwI2ubNm2cCuaCgIOnYsaPp5wYAAADA9+mYIFZZfIFlArdRo0bJnj17HLd37dol7du3lxo1asjAgQPlm2++kbFjx3q1jAAAAAAQrwO3HTt2SPXq1R23Fy9eLKVLl5Y5c+ZInz59ZMqUKY4JuQEAAAAgPrFMH7dLly5JhgwZHLd//vlnqVOnjuN2qVKl5Pjx414qHQAAAICYpIMSwgdb3DRo0/5t6u7du7Jt2zYpU6aM4/5r166Z0SUBAAAAIL6xTOBWt25d05ftl19+kUGDBkny5MmlYsWKjvt37twpuXLl8moZAQAAACBep0qOGTNGGjVqJJUrV5YnnnhCPv74Y0mcOLHj/g8//FBq1arl1TICAAAAiGMtSD7CMoGbTgGwbt06uXLligncEiRI4HT/Z599ZtYDAAAAQHxjmcDNLnXq1C7Xp02b1uNlAQAAAAArsFzgBgAAACDuY1RJ95BaCgAAAAAWR4sbAAAAAI+jvc09tLgBAAAAgMURuAEAAACAxZEqCQAAAMDjGJzEPbS4AQAAAIDFEbgBAAAAgMWRKgkAAADA42hBcg/vFwAAAABYHIEbAAAAAFgcqZIAAAAAPI5RJd1DixsAAAAAWBwtbgAAAAA8jvY299DiBgAAAAAWR+AGAAAAABZHqiQAAAAAj2NsEvfQ4gYAAAAAFkfgBgAAAAAWR6okAAAAAI/zZ1xJt9DiBgAAAAAWR4sbAAAAAI9jcBL30OIGAAAAABZH4AYAAAAAFkeqJAAAAACP82NwErfQ4gYAAAAAFkfgBgAAAAAWR6okAAAAAI9jVEn30OIGAAAAABZH4AYAAAAAFkeqJAAAAACP82dUSbfQ4gYAAAAAFkeLGwAAAACPY3AS99DiBgAAAAAWR+AGAAAAABZHqiQAAAAAjyNV0j20uAEAAACAxRG4AQAAAIDFkSoJAAAAwOP8mMfNLbS4AQAAAIDFEbgBAAAAgMWRKgkAAADA4/zJlHQLLW4AAAAAYHG0uAEAAADwOAYncQ8tbgAAAABgcQRuAAAAAGBxpEoCAAAA8Dg/MiXdQosbAAAAAFgcgRsAAAAAWBypkgAAAAA8jlEl3UOLGwAAAABYHC1uAAAAADzOnwY3t9DiBgAAAAAWR+AGAAAAABZHqiQAAAAAj2NwEvfQ4gYAAAAAFkfgBgAAAAAWR6okAAAAAI/zI1PSLbS4AQAAAIDFEbgBAAAAgMWRKgkAAADA48iUdA8tbgAAAABgcbS4AQAAAPA4f0YncQstbgAAAABgcQRuAAAAAGBx8SJVMjTU5u0iANFy536ot4sAuO3Sxne9XQQgWgJqjvF2EYBoubV2mPgiEiXdQ4sbAAAAAFgcgRsAAAAAWFy8SJUEAAAAYDHkSrqFFjcAAAAAsDgCNwAAAACwOFIlAQAAAHicH7mSbqHFDQAAAAAsjhY3AAAAAB7nR4ObW2hxAwAAAACLI3ADAAAAAIsjVRIAAACAx5Ep6R5a3AAAAADA4gjcAAAAAMDiSJUEAAAA4HnkSrqFFjcAAAAAsDha3AAAAAB4nB9Nbm6hxQ0AAAAALI7ADQAAAAAsjlRJAAAAAB7nR6akW2hxAwAAAACLI3ADAAAAAIsjVRIAAACAx5Ep6R5a3AAAAADA4gjcAAAAAMDiSJUEAAAA4HnkSrqFFjcAAAAAsDha3AAAAAB4nB9Nbm6hxQ0AAAAALI7ADQAAAAAsjlRJAAAAAB7nR6akW2hxAwAAAAA3TZ8+XXLkyCFJkyaV0qVLy+bNmyPdds6cOVKxYkUJCAgwS40aNR66vSsEbgAAAADghiVLlkifPn1kxIgRsm3bNilSpIjUrl1bzp4963L7n376SZo1ayZr166VDRs2SNasWaVWrVpy4sSJKL+mn81ms0kcd/NunN9FxFF37od6uwiA25IlTuDtIgDRElBzjLeLAETLrbXDxBftOHZNrKJItpRuba8tbKVKlZJp06aZ26GhoSYY6969uwwcOPCRjw8JCTEtb/r4Vq1aRek1aXEDAAAAEK/duXNHrl696rToOlfu3r0rW7duNemOdv7+/ua2tqZFxc2bN+XevXuSNm3aKJeRwA0AAABAvDZ27FhJnTq106LrXDl//rxpMcuQIYPTer19+vTpKL3egAED5Mknn3QK/h6FUSUBAAAAeJ6FRpUcNGiQ6bMWVpIkSWLltd566y1ZvHix6femA5tEFYEbAAAAgHgtSZIkUQ7UAgMDJUGCBHLmzBmn9Xo7Y8aMD33s22+/bQK3H3/8UQoXLuxWGUmVBAAAAOBxfhb6zx2JEyeWEiVKyOrVqx3rdHASvV22bNlIHzd+/HgZM2aMrFixQkqWLCnuosUNAAAAANygaZWtW7c2AdgzzzwjkyZNkhs3bkjbtm3N/TpSZObMmR395MaNGyfDhw+XRYsWmbnf7H3hnnjiCbNEBYEbAAAAALjh5ZdflnPnzplgTIOwokWLmpY0+4Alx44dMyNN2s2cOdOMRvnSSy85PY/OAzdy5MgovSbzuAEWxjxu8EXM4wZfxTxu8FW+Oo/brn+ui1UUyhK1Vi9voo8bAAAAAFgcgRsAAAAAWBx93AAAAADE52ncfAItbgAAAABgcQRuAAAAAGBxpEoCAAAA8DxyJd1CixsAAAAAWBwtbgAAAAA8zo8mN7fQ4gYAAAAAFkfgBgAAAAAWR6okAAAAAI/zI1PSLbS4AQAAAIDFEbgBAAAAgMWRKgkAAADA48iUdA8tbgAAAABgcbS4AQAAAPA8mtzcQosbAAAAAFgcgRsAAAAAWBypkgAAAAA8zo9cSbfQ4gYAAAAAFkfgBgAAAAAWR6okAAAAAI/zI1PSLbS4AQAAAIDFEbgBAAAAgMWRKgkAAADA48iU9OEWt3v37kmuXLlk79693i4KAAAAAFiGpVrcEiVKJLdv3/Z2MQAAAADENprcfLfFTXXt2lXGjRsn9+/f93ZRAAAAAMASLNXipn7//XdZvXq1/PDDD1KoUCFJkSKF0/3Lli3zWtkAAAAAwBssF7ilSZNGXnzxRW8XAwAAAEAs8iNX0rcDt7lz53q7CAAAAABgKZYL3OzOnTsn+/fvN/8ODg6W9OnTe7tIAAAAAOAVlhuc5MaNG9KuXTvJlCmTVKpUySxPPvmktG/fXm7evOnt4gEAAACIAX5+1ll8geUCtz59+sjPP/8s33zzjVy+fNksX331lVnXt29fbxcPAAAAADzOcqmSn3/+uSxdulSqVKniWFe3bl1JliyZNGnSRGbOnOnV8gEAAACAxPfATdMhM2TIEGF9UFAQqZIAAABAHOEjGYqWYblUybJly8qIESPk9u3bjnW3bt2SUaNGmfsAAAAAIL6xXIvb5MmTpXbt2pIlSxYpUqSIWbdjxw5JmjSprFy50tvFAwAAABATaHLz7cCtYMGCcvDgQVm4cKHs27fPrGvWrJm0aNHC9HMDAAAAgPjGcqmSKnny5NKhQwd55513zPLqq68StHnIkk8WSt3a1aR0icLSsnkT2b1r50O3X7VyhTSsV8ds37hhPfll3c9O96/+8Qfp0rGdVKlQWooVyiv79+2N5T1AfLV0ySJpULeGVCpdVNq1fFn27H543V29aoW83PA5s32LxvVl/S/OdXf08MFSplh+p6VX146xvBeIbxYvWih1alaTUsUKSYumjWXXzofX2x9Wfi/1n3/WbP9ig4jHXJvNJtOnTpbqlSvIM8ULS8f2beTo0SOxvBeIjzo1KCn7Pukul1YOknUz2knJvE9Gum3CBP4yqFVF2bOgq9l+0/sdpWapXJFu369ZObm1dphM6ForlkoP+CZLBm4nT56UTz/9VKZNmyZTpkxxWhB7Vq5YLu9MeEs6de4qiz5dJnnyBMtrnV6VixcuuNx++/ZtMmhAX2nQ6CX55LMvpEq1GtKnZzf56+ABp/6JRYuVkB69+3lwTxDfrFr5vUx+Z5y82uk1+XjRUsmdJ6/0eq2jXLzouu7u3P6HDB/UX+o1aCQff/K5VKpSXV7v013+/uug03ZlylWQ71b97FhGj53goT1CfLDi++Xy9vix0um1rrL4sy8kODivdOnUXi5Edsz9Y5sM7N9XGjZ6SZYs/VKqVqsuvbp3lYNhjrlzP5gjnyycL0NHjJQFn3xqLnp26dhe7ty548E9Q1z3UtX8Mq5LTXnj43VStuMc2fn3Gfl6fHNJnya5y+1Htq8qrz5fXPpMXSnF2syU97/eKkvGNJYiT2eMsG2J4EzSvl5x85yI+/ws9J8vsFzg9tFHH0nOnDnNhNtvv/22vPvuu45l0qRJ3i5enLZg3kfS6MXGUr/hi5Ir19MyZPgoSZosqXz5xecut/9kwXwpV76CtG7bXp56Kpd07d5T8uXPL4s/WejY5vl69aVTl65SpgwDyyD2fLLgI6nfqLE8X7+R5Mz1tAwYMsL0i/32y2Uut1/yyXwTlL3Sur3kfCqXdOraQ4Lz5Zeli/+ruypx4sSSLjC9Y0mVKrWH9gjxwfyP50qjl5pIAz3mPv20DB0xytTbL5e5PuYuXDBPylWoKG3avSpP5col3Xr0enDMXbTA0dq2cP486dCpi1StVkPyBOeV/40dL+fOnpU1q3/08N4hLuvRuIzM/e4Pmb9ih+w7el66T/xObt2+J63rFHW5ffOahWT8ot9k5aa/5MipyzLn663m3z2blHHaLkXSRDJ3SEN57e3v5PK1Wx7aG8B3WC5wGzZsmAwfPlyuXLkiR44ckcOHDzuWQ4cOebt4cda9e3dl7597pHSZco51/v7+UrpMWdm5Y7vLx+j6sNursuXKR7o9EFt1d//eP6VU6TJOdbdU6bKya6frurh753Zzf1hlypaXXTt3OK3btuV3qVOtgjRpUFfGvTFKrly+HEt7gfjm3t0Hx9wyZZ2PuWXKlJOdO/5w+Zid27dHuAimF890vTrxzz9y/vw5p+NyypQppVDhIpE+J+CuRAn9pVieTLJm62HHOptNZM22w/JMgSwuH5M4UQK5ffe+07pbd+5LuUJZndZN6lVHVmw8KGu3/ffcACw8OInO1da0aVPzAwbPuXTpkoSEhEjadOmc1qdLFyhHDrs+gJ4/f97l9hfOn4/VsgJhXb50+UHdTRvotD4gXTo5csT1xR6to2nTOtfdAK27F/6ru2XLVTDpv09mziIn/jkmM6dOkt7dOsmcjxdJggQJYmlvEF9cuvzgmJsuwjE0nRw+fCjSY64eY8Nvf/7feqtBm1kXGPE59bFATAhMndz0WTt76brT+rOXbkhwNuf6affjlkOmle7XHcfk0MmLUrV4TqlfMa8k8P8vPa1x1QJSNHcmqdD5/VjfB1iHn29kKFqG5QI3TZH87LPPZODAgdF6vObxh8/lD/FLLEmSJImhEgKID2o+W9fx76dz55GncwfLi/Vqy7YtmyO01gEAItdv6kqZ0e952fFxF7GJyKETl2Teiu2O1Mos6VPJhG615Pn+C+XOvRBvFxewLMsFbmPHjpXnn39eVqxYIYUKFZJEiRI53T9x4sRHPl4n6w5r8NDhMmTYyFgpb1wREBBgWhHCD0SiLRDhr/DaBQYGut4+0PX2QGxIE5DmQd296NyicOnChUjrrtbR8AOXXHpIXVeZs2SVNGkC5J/jxwjc8NgC0jw45oYfiERv67HVFV0ftlXYsf2/9TYwMP2DdecvSPr0QU7bBOfNGwt7gfjo/JWbcj8kVIICnnBaHxSQQk5fvB7pY5oM+1SSJEog6VInl5Pnr8n/OlaXw6cepJ9r6mWGtE/Ihvc6OB6jrXoVCmeXzg1LSepab0poqIZ8iGtocHOP5fIRNfDSibbPnDkju3btkj/++MOxbP83j/9hBg0aZPrHhV36vT7II2X3ZYkSJZZ8+QvIpk0bHOtCQ0Nl88aNUriI687Gun5zmO3Vxg3rI90eiK26qwOL/L5po1Pd/X3zRilU2HVdLFi4qLk/rM0bN5i+QJE5e+a0XLly2QxSAjyuRIn/PeZudD7m6jG4cJFiLh9TuGhR2bRxY8RjbtEH9TxzliwmeAt7HL9+/brpuxnZcwLuunc/VP44cEqqFs/hlO6m6Y+b9/zz0Mdqa5oGbRqUNaiUV779bb9Zr33aSrSdJaVffc+xbN13Uhb/uMv8m6ANsGiLm87b9uGHH0qbNm2i9XhNiQyfFnnzLl/4qHilVRsZPmSg5C9QUAoWKiyL5n9shvOv36CRuX/o4AESFBQkPXr1NbebvdJSOrRtJfM+/lAqVqwiK1d8J3/u2SPDRox2PKee6J4+dUrOnj1rbh85ctjR4mG/Ogw8rmavtJExwwdJvvwFJX/BQrJk0Ty5feuWPFe/obl/1NCBkj4oSF7r0cfcfrlZS+nSobUsnDdXylesLKtWLpe9f+6WgcMetNbfvHlDPpg9Q6pWryVpAwPlxPFjMm3yO5IlazYzGiUQE1q2bivDBg+QAv8ecxf8e8xt0PDBMXfIoNclKCiD9Oz94Jjb4pVW0r5NS/n4ow+lUqXKZjqBPbt3y7CRD465fn5+0qJlK5kze6Zkz5bdBHI6p5vW/WrVa3h1XxG3TPlso8wZWF+2HjglW/aelG4vPSPJkyaSeSseDPD0/qD6cvLcNRn+/hpzu1S+J+XJwFSy46/TkjkwpQxpU1n8/fxk4ifrzf3Xb92VP4886KNpd+P2Xbl49VaE9UB8ZrnATYOu8uXLe7sY8VLtZ+vKpYsXZeb0qXLh/DkJzptPps+a40h9PH3qpDnQ2hUtWlzefOttmT5tkkyb/K5ky55DJk6eZvoD2f28do2MGDbYcXtg/wcnzjpFQOfXunt0/xB31axdRy5fuihzZk41qWS5g/PKu9NnO1IfT58+JX5hBjwqXLSYjH5zvMyePkVmTZskWbNll/ETp0qup3Ob+/39E5j5CJd/85Vcu3ZVAtMHSemy5aXja93NFAFATHi2zoNj7oxpU8zAInrMnTH7/TDH3FPi7/dfvS1arLiMHf+2TJsySaZOmmiOuZOmTpfcYY65bdt3MMHf6JHDTd0tVryEeU76eSMmLV37pxmkZHibyibFUedcqz9gkRmgRGUNSuXUSpYkcUIZ0a6K5HwywARpOhVA+ze/lCs3mF8w3iNX0i1+Np34xWKpkqdOnYrRybZpcYOvunM/1NtFANyWLDGjbsI3BdQc4+0iANFya+0w8UVHLtwWq8iRLqlYneVa3DZv3ixr1qyRb7/9VgoUKBBhcJJly1xPqAsAAAAAcZXlArc0adJIo0YP8vsBAAAAxE1+5Er6duA2d+5cbxcBAAAAACzFctMBAAAAAAAs3uKWM2dOM6RxZA4dOuTR8gAAAACIeQ855YcvBG69evVyun3v3j0z+faKFSukf//+XisXAAAAAHiL5QK3nj17ulw/ffp02bJli8fLAwAAACDm0eAWR/u41alTRz7//HNvFwMAAAAAPM5nArelS5dK2rRpvV0MAAAAAIi/qZKjR4+Wvn37SoUKFZwGJ7HZbHL69Gk5d+6czJgxw6tlBAAAABAzGJzERwO3UaNGSefOnaV+/fpOgZu/v7+kT59eqlSpInnz5vVqGQEAAAAgXgdu2rKmRo4c6e2iAAAAAIClWCZwUw+bvw0AAABAXMK5v88Gbnny5Hlk8Hbx4kWPlQcAAAAArMBSgZv2c0udOrW3iwEAAAAAlmKpwK1p06YSFBTk7WIAAAAAiGX0kvLRedzo3wYAAAAAPjKqJAAAAIC4j2YbHw3cQkNDvV0EAAAAALAky6RKAgAAAAAs3uIGAAAAIP5giAv30OIGAAAAABZH4AYAAAAAFkeqJAAAAACP82NcSbfQ4gYAAAAAFkeLGwAAAADPo8HNLbS4AQAAAIDFEbgBAAAAgMWRKgkAAADA48iUdA8tbgAAAABgcQRuAAAAAGBxpEoCAAAA8Dg/ciXdQosbAAAAAFgcgRsAAAAAWBypkgAAAAA8zo9xJd1CixsAAAAAWBwtbgAAAAA8jwY3t9DiBgAAAAAWR+AGAAAAABZHqiQAAAAAjyNT0j20uAEAAACAxRG4AQAAAIDFkSoJAAAAwOP8yJV0Cy1uAAAAAGBxBG4AAAAAYHGkSgIAAADwOD/GlXQLLW4AAAAAYHG0uAEAAADwOAYncQ8tbgAAAABgcQRuAAAAAGBxBG4AAAAAYHEEbgAAAABgcQRuAAAAAGBxjCoJAAAAwOMYVdI9tLgBAAAAgMXR4gYAAADA4/yEJjd30OIGAAAAABZH4AYAAAAAFkeqJAAAAACPY3AS99DiBgAAAAAWR+AGAAAAABZHqiQAAAAAjyNT0j20uAEAAACAxRG4AQAAAIDFkSoJAAAAwPPIlXQLLW4AAAAAYHG0uAEAAADwOD+a3NxCixsAAAAAWByBGwAAAABYHKmSAAAAADzOj0xJt9DiBgAAAAAWR+AGAAAAABZHqiQAAAAAjyNT0j20uAEAAACAxRG4AQAAAIDFkSoJAAAAwPPIlXQLLW4AAAAAYHG0uAEAAADwOD+a3NxCixsAAAAAuGn69OmSI0cOSZo0qZQuXVo2b9780O0/++wzyZs3r9m+UKFCsnz5crdej8ANAAAAANywZMkS6dOnj4wYMUK2bdsmRYoUkdq1a8vZs2ddbr9+/Xpp1qyZtG/fXv744w9p0KCBWXbv3h3l1/Sz2Ww2ieNu3o3zu4g46s79UG8XAXBbssQJvF0EIFoCao7xdhGAaLm1dpj4otv3xTKSutmBTFvYSpUqJdOmTTO3Q0NDJWvWrNK9e3cZOHBghO1ffvlluXHjhnz77beOdWXKlJGiRYvKrFmzovSatLgBAAAAQBTdvXtXtm7dKjVq1HCs8/f3N7c3bNjg8jG6Puz2SlvoItveFQYnAQAAABCv3blzxyxhJUmSxCzhnT9/XkJCQiRDhgxO6/X2vn37XD7/6dOnXW6v66MqXgRuyRMzYk1s0Qo+duxYGTRokMuKjceTnJSzWEG9ha+i7sYuX003szrqLWIqPTE2jfzfWBk1apTTOu2/NnLkSLEKUiXx2AdjreThr1AAVka9ha+i7sIXUW/hCwYNGiRXrlxxWnSdK4GBgZIgQQI5c+aM03q9nTFjRpeP0fXubO8KgRsAAACAeC1JkiSSKlUqpyWyFuLEiRNLiRIlZPXq1Y51OjiJ3i5btqzLx+j6sNurVatWRbq9KxZqoAQAAAAA6+vTp4+0bt1aSpYsKc8884xMmjTJjBrZtm1bc3+rVq0kc+bMJk1Y9ezZUypXrizvvPOOPPfcc7J48WLZsmWLvPfee1F+TQI3AAAAAHCDDu9/7tw5GT58uBlgRIf1X7FihWMAkmPHjpmRJu3KlSsnixYtkqFDh8rgwYMld+7c8uWXX0rBggWj/JoEbngs2oSsHTfpbAxfQr2Fr6LuwhdRbxFXdevWzSyu/PTTTxHWNW7c2CzRFS8m4AYAAAAAX8bgJAAAAABgcQRuAAAAAGBxBG4AEEM0n93Pz08uX77s7aIAAIA4hsANTnRUnO7du8tTTz1lOhFnzZpV6tWrF2Heieho06aNNGjQIEbKibhP64sGQW+99ZbTeh2BSdcDvkZHH+vSpYtky5bNHF910tXatWvLb7/9Zu7Xeq31G/A2rYsPW0aOHOntIgLxEqNKwuHIkSNSvnx5SZMmjUyYMEEKFSok9+7dk5UrV0rXrl1l37593i4i4pmkSZPKuHHjpFOnThIQEBAjz3n37l0zcSbgaS+++KKpfx9//LG5OHbmzBlzUezChQveLhrg5NSpU45/L1myxAx3vn//fse6J554QqyA4zniG1rc4PDaa6+ZK2mbN282Jxh58uSRAgUKmAkGN27cKO3atZPnn3/e6TEa2AUFBckHH3xgbi9dutQEfMmSJZN06dJJjRo1zGSEenVOT1a++uorxxU7V8OkAmFp/dFWCfvkla58/vnnpp5qC0aOHDnMxJZh6boxY8aYiTBTpUolHTt2lI8++shcoPj2228lODhYkidPLi+99JLcvHnT1FN9jAaKPXr0kJCQEMdzzZ8/30y0mTJlSlOu5s2by9mzZ2P1PUDcoOmzv/zyi7kQUbVqVcmePbuZsHXQoEHywgsvmDqnGjZsaI6P9ttq5syZkitXLnOCqvVV62FYuv37779vHqt1WecG+vrrr5222b17t9SpU8eccOscQy1btpTz5897aO/ha/T4Zl9Sp05t6pj99qxZs6RChQpO2+vEw2HrrD3D5s033zT1TY+3o0ePlvv370v//v0lbdq0kiVLFpk7d67T8+zatUuqVavmOIfQ4/X169cjPO8bb7whTz75pPk+APEJgRuMixcvmkkDtWUtRYoUEe7Xg+6rr75qtgl7JU5PfPVkVych1PXNmjUzAd7evXtNYNaoUSPRGSf69esnTZo0kWeffdZsp4tORAg8TIIECcwP/9SpU+Wff/6JcP/WrVtNvWratKn5wdcLBMOGDTOBWVhvv/22FClSRP744w9zv9J6O2XKFFm8eLGp11pf9cR3+fLlZtGT49mzZ5uLEWEvVGgQuGPHDpPSpq3UeiIBPIoGTLpovblz506E+3///XfzV09k9fhov/3FF19Iz549pW/fvib40tbntm3bytq1a50eP2rUKPNd2Llzp9StW1datGhhjuv2oFFPhosVKyZbtmwx9V1b+3R7ILasWbNGTp48KevWrZOJEyeaedz04q9eFNu0aZN07tzZ1Gf7sV0v8mrqsN6v9f+zzz6TH3/8McIcWdpKra1/q1atMucgQLyi87gBmzZt0vn8bMuWLXvodvnz57eNGzfOcbtevXq2Nm3amH9v3brVPMeRI0dcPrZ169a2+vXrx3DJEVeFrS9lypSxtWvXzvz7iy++MPVMNW/e3FazZk2nx/Xv39/UU7vs2bPbGjRo4LTN3LlzzXP89ddfjnWdOnWyJU+e3Hbt2jXHutq1a5v1kfn999/N89gfs3btWnP70qVLj7n3iIuWLl1qCwgIsCVNmtRWrlw526BBg2w7duxw3K91R+t3WLpdhw4dnNY1btzYVrduXafHDR061HH7+vXrZt33339vbo8ZM8ZWq1Ytp+c4fvy42Wb//v0xvp+IW/R4mTp1asftESNG2IoUKeK0zbvvvmuOtWGP33o7JCTEsS44ONhWsWJFx+379+/bUqRIYfvkk0/M7ffee898P7T+2n333Xc2f39/2+nTpx3PmyFDBtudO3diaW8Ba6PFDUZU52HXVjd7aoNesf3+++9NC5vSFo3q1aubVEmdFX7OnDly6dKlWC034gdNL9MURm3JDUtva7/MsPT2wYMHnVIcNb0xPE0p0/QzO03n0VSfsH03dF3YVEht4dPBenRwCU2XrFy5sll/7NixGNpTxGWagq4tEJrGqNkH2spbvHjxCC3EUanj4b8LhQsXdvxbsyY0Ldhed7WFWFvo7K1+uuTNm9fc9/fff8fwXgIPaAq7v7+/0/FUzw/CZlRoOqS9nmqd1vOIsFk/WtdDQ0Od+tfpc9CvDfEVgRsM7ROhOeyPGoBE+wkdOnRINmzYIAsWLJCcOXNKxYoVHQdhTV3QYC5//vwmvU3zzw8fPuyhvUBcValSJZNCo/2BosNV+m+iRImcbmv9d7VOTxrCpvHoCfHChQtNKo+msdk7yANRHXCnZs2aJmV3/fr1JtVWU8ge18PqrvYR0gsO27dvd1r0Aod+twB3aDAW/mKvppE/7jH2cY7nQHxB4AZDOwrrSen06dPNCWp49nmp9OqYdgzWVje9Sqx9LcIfhPUKmfa30P5EelXMfnKr/w7bCgK4Q6cF+Oabb8xFA7t8+fI5hlK309s6sI5eSIhJelFDR//TcujFCm2xYGASPC69yGU/5upJbfhjZGR1XB8XVdqqt2fPHtOi/PTTTzstnATDXenTpzdTB4UN3vRCwOPSuq6tw2HPQbSua6DIICTAAwRucNCgTU8adKQzHalPr8Zq6oIO4FC2bFmndEl72lrr1q0d67WzsQ4koZ3fNXVs2bJlZt4iPRgrPWnQjvOa8qCjmbm6QgdERtNjdMAFrY92OmCDdlTXAUMOHDhg6uW0adPMYDgxTdMj9eKDtiRrq7Omu+nrAlGhQb8OEKKZCnoc1EwEHXxh/PjxUr9+fccxUuuznhTb08x1BD69SKYjS+oxWQd50GOrO3VcB53SgUp08ChtKdb0SJ3mRS+8cTEN7qpSpYr5bde6q3VJzx000+Zx6fFdW6T1vEIH4tH0Xp1XVkdA1TRLAARuCEPnFdq2bZsZqlpPiAsWLGhSevREQk8awg7RnilTJtNCp8Px2mkKmY4epSOaaYvH0KFDzdDsOgS16tChg7lqpv2N9Ipd+KvIwKPocNJh02q0JeHTTz81I0NqfdW5hnSb2BjpUeusnkDryba2dmjLm45WCUSF9isrXbq0vPvuuyY9UeurpkvqcVEvNig9Xmq6edasWc0IkEozHCZPnmzqmvYZ0pFONeNBT56jSo/TerzVIK1WrVrmIkivXr3MaMFh+yABUaEXY2fMmGECNu2TplMIxcTFMu13rBcU9CJDqVKlzBQt2m/e/v0AIOKnI5R4uxDwLdpfInPmzObkQYf7BwAAABC7Esby8yMO0ZYOTXHUq8J6pVYnjQUAAAAQ+wjcEGXab01HkcySJYtJGUuYkOoDAAAAeAKpkgAAAABgcfRKBgAAAACLI3ADAAAAAIsjcAMAAAAAiyNwAwAAAACLI3ADAAAAAIsjcAOAOCpHjhzSpk0bx+2ffvpJ/Pz8zF+rltETqlSpIgULFvT5/QAAxC8EbgAQC3SuQw2S7EvSpEklT5480q1bNzlz5oz4kuXLl8vIkSO9WgZ9D/W9AwAgvmIGZQCIRaNHjzYT19++fVt+/fVXmTlzpgmEdu/eLcmTJ/doWSpVqiS3bt2SxIkTu/U4Le/06dO9HrwBABCfEbgBQCyqU6eOlCxZ0vz71VdflXTp0snEiRPlq6++kmbNmrl8zI0bNyRFihQxXhZ/f3/T8gcAAHwPqZIA4EHVqlUzfw8fPmz+ar+oJ554Qv7++2+pW7eupEyZUlq0aGHuCw0NlUmTJkmBAgVMwJUhQwbp1KmTXLp0yek5bTab/O9//5MsWbKYVryqVavKnj17Irx2ZH3cNm3aZF47ICDABIyFCxeWyZMnO8qnrW0qbOqnXUyX8XFoMPzcc8/Jk08+KUmSJJFcuXLJmDFjJCQkxOX2W7dulXLlykmyZMlMq+isWbMibHPnzh0ZMWKEPP300+Y5s2bNKq+//rpZDwCAJ9HiBgAepAGa0pY3u/v370vt2rWlQoUK8vbbbztSKDUA0r5ybdu2lR49ephgb9q0afLHH3/Ib7/9JokSJTLbDR8+3ARFGnzpsm3bNqlVq5bcvXv3keVZtWqVPP/885IpUybp2bOnZMyYUfbu3Svffvutua1lOHnypNlu/vz5ER7viTJGlZZDg+A+ffqYv2vWrDGve/XqVZkwYYLTthpYajmaNGliWj4//fRT6dKli0kjbdeunSMofeGFF0yKa8eOHSVfvnyya9cueffdd+XAgQPy5ZdfxljZAQB4JBsAIMbNnTvXpofYH3/80Xbu3Dnb8ePHbYsXL7alS5fOlixZMts///xjtmvdurXZbuDAgU6P/+WXX8z6hQsXOq1fsWKF0/qzZ8/aEidObHvuuedsoaGhju0GDx5sttPnt1u7dq1Zp3/V/fv3bTlz5rRlz57ddunSJafXCftcXbt2NY8LLzbKGBndTsvxMDdv3oywrlOnTrbkyZPbbt++7VhXuXJl83zvvPOOY92dO3dsRYsWtQUFBdnu3r1r1s2fP9/m7+9v9jOsWbNmmcf/9ttvjnX6HkZlPwAAiC5SJQEgFtWoUUPSp09vUuyaNm1qWoK++OILyZw5s9N22toT1meffSapU6eWmjVryvnz5x1LiRIlzHOsXbvWbPfjjz+aVqvu3bs7pTD26tXrkWXTVjFtIdNt06RJ43Rf2OeKjCfK6A5NebS7du2aKUvFihXl5s2bsm/fPqdtEyZMaFoL7bSlTW+fPXvWpFDa909b2fLmzeu0f/Z0V/v+AQDgCaRKAkAs0v5hOg2ABgra/ys4ONgMEhKW3qd9v8I6ePCgXLlyRYKCglw+rwYY6ujRo+Zv7ty5ne7XYFH7rEUlbTO6c5p5oozu0D5zQ4cONSmSmh4ZlpYzLO0HF34AGP2c1JEjR6RMmTJm/zRtVMv5sP0DAMATCNwAIBY988wzjlElI6ODXoQP5rR/lQZECxcudPmYyIIJT7JSGS9fviyVK1eWVKlSmSkYdGASHSxF+9INGDDAlNVd+phChQqZUUBd0VZUAAA8hcANACxIAw9NMSxfvrxTCmB42bNnN3+1deipp55yrD937lyEkR1dvYbSOeU0pTMykaVNeqKMUaUjZV64cEGWLVtm5quzs4/eGZ4OuBJ+2gUdcETlyJHDsX87duyQ6tWrRyl1FACA2EQfNwCwIB3tUIex1+Hsw9NRKLWFSWnApSM3Tp061Qy5b6dD9D9K8eLFzTD4uq39+ezCPpc9uAm/jSfKGFUJEiSIUG7tVzdjxgyX22v5Zs+e7bSt3tZWQu2jZ9+/EydOyJw5cyI8Xicy18APAABPocUNACxI0/50sIyxY8fK9u3bzdD5Gvxoq5UOmqHzrL300ksm0OjXr5/ZTof11yHuddCR77//XgIDAx/6GpqeOXPmTKlXr54ULVrUDOmv0wLoQB7aX2zlypVmO3sgo8P967QFGiTpQCueKGNYW7ZsMVMKhFelShUzH5v2l2vdurUpp7aQ6fQFYQO58H3cxo0bZ/qzad+2JUuWmH147733HFMYtGzZ0kwT0LlzZzMQibYsaqCq74+u1/fnUWmwAADEmGiPRwkAeOR0AL///vtDt9Mh5FOkSBHp/e+9956tRIkSZgqBlClT2goVKmR7/fXXbSdPnnRsExISYhs1apQtU6ZMZrsqVarYdu/eHWGI+vDTAdj9+uuvtpo1a5rn17IULlzYNnXqVMf9Om1A9+7dbenTp7f5+flFmBogJssYGX3NyJYxY8aYbXR4/jJlypjnf/LJJ00ZVq5cGWGfdTqAAgUK2LZs2WIrW7asLWnSpKYc06ZNi/C6OjXAuHHjzPZJkiSxBQQEmH3Vfbly5YpjO6YDAADENj/9X8yFgQAAAACAmEYfNwAAAACwOAI3AAAAALA4AjcAAAAAsDgCNwAAAACwOAI3AAAAALA4AjcAAAAAsDgCNwAAAACwOAI3AAAAALA4AjcAAAAAsDgCNwAAAACwOAI3AAAAALA4AjcAAAAAsDgCNwAAAAAQa/s/8/BSQy2n/pAAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== Training Inception-v3 on Fold 2/5 ===\n", "\n", "Training on 1884 images\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "23185c94450f4e4f87b84dbdb895f8a0", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 1:   0%|          | 0/118 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 1/10:\n", "  Train Loss: 0.7703, Train Acc: 78.66%\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "faf9c48e3efc426e8f4c2321f819652d", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 2:   0%|          | 0/118 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 2/10:\n", "  Train Loss: 0.2373, Train Acc: 92.68%\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "25784fa12530423f8ecb490d14059b11", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 3:   0%|          | 0/118 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 3/10:\n", "  Train Loss: 0.2727, Train Acc: 93.31%\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "df97a9c7693743dc99151be6453c0fb0", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 4:   0%|          | 0/118 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 4/10:\n", "  Train Loss: 0.1534, Train Acc: 96.23%\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "06ee15329e4e40ad89ef0944fa49a697", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 5:   0%|          | 0/118 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 5/10:\n", "  Train Loss: 0.0952, Train Acc: 97.98%\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7081c1c1bbca4dbe93fab0908b6b69c4", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 6:   0%|          | 0/118 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 6/10:\n", "  Train Loss: 0.1529, Train Acc: 96.28%\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a0834a3f28aa41e8ad9a69756a349801", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 7:   0%|          | 0/118 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 7/10:\n", "  Train Loss: 0.0631, Train Acc: 98.62%\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8a4112160a22402487638822fc5c1fc1", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 8:   0%|          | 0/118 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 8/10:\n", "  Train Loss: 0.1296, Train Acc: 96.92%\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0ea219779c94470eb23e81f6d97ff187", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 9:   0%|          | 0/118 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 9/10:\n", "  Train Loss: 0.1039, Train Acc: 97.72%\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7d01b1b6961e434a9bebc8f339e5f96f", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 10:   0%|          | 0/118 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 10/10:\n", "  Train Loss: 0.0476, Train Acc: 98.73%\n", "Saved final model to inception_v3_fold2.pth\n", "\n", "Fold 2 Results:\n", "  Final Train Acc: 98.73%\n", "  Test Loss: 0.0311, Test Acc: 99.29%\n", "\n", "Generating confusion matrix for Fold 2...\n", "Confusion Matrix for Fold 2:\n", "[[659   0   2   0]\n", " [  0 997   0   1]\n", " [ 10   0 265   0]\n", " [  0   4   0 462]]\n", "\n", "Classification Report for Fold 2:\n", "              precision    recall  f1-score   support\n", "\n", "        Cyst       0.99      1.00      0.99       661\n", "      Normal       1.00      1.00      1.00       998\n", "       Stone       0.99      0.96      0.98       275\n", "       <PERSON><PERSON>       1.00      0.99      0.99       466\n", "\n", "    accuracy                           0.99      2400\n", "   macro avg       0.99      0.99      0.99      2400\n", "weighted avg       0.99      0.99      0.99      2400\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== Training Inception-v3 on Fold 3/5 ===\n", "\n", "Training on 1937 images\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d2cb3e4de76248299b1372db1c7c090f", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 1:   0%|          | 0/122 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "ValueError", "evalue": "Expected more than 1 value per channel when training, got input size torch.Size([1, 768, 1, 1])", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[26]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Run the training\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m inception_v3_results = \u001b[43mtrain_inception_v3_on_folds\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnum_epochs\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m10\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbatch_size\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m16\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[25]\u001b[39m\u001b[32m, line 39\u001b[39m, in \u001b[36mtrain_inception_v3_on_folds\u001b[39m\u001b[34m(num_epochs, batch_size)\u001b[39m\n\u001b[32m     31\u001b[39m results = {\n\u001b[32m     32\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mtrain_loss\u001b[39m\u001b[33m'\u001b[39m: [], \u001b[33m'\u001b[39m\u001b[33mtrain_acc\u001b[39m\u001b[33m'\u001b[39m: [],\n\u001b[32m     33\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mtest_loss\u001b[39m\u001b[33m'\u001b[39m: \u001b[32m0\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mtest_acc\u001b[39m\u001b[33m'\u001b[39m: \u001b[32m0\u001b[39m,\n\u001b[32m     34\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mfinal_train_acc\u001b[39m\u001b[33m'\u001b[39m: \u001b[32m0\u001b[39m\n\u001b[32m     35\u001b[39m }\n\u001b[32m     37\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m epoch \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(num_epochs):\n\u001b[32m     38\u001b[39m     \u001b[38;5;66;03m# Train for one epoch\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m39\u001b[39m     train_loss, train_acc = \u001b[43mtrain_epoch\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtrain_loader\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcriterion\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moptimizer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mepoch\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     41\u001b[39m     \u001b[38;5;66;03m# Print statistics\u001b[39;00m\n\u001b[32m     42\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m'\u001b[39m\u001b[33mEpoch \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mepoch+\u001b[32m1\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m/\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnum_epochs\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m:\u001b[39m\u001b[33m'\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[22]\u001b[39m\u001b[32m, line 17\u001b[39m, in \u001b[36mtrain_epoch\u001b[39m\u001b[34m(model, train_loader, criterion, optimizer, epoch)\u001b[39m\n\u001b[32m     14\u001b[39m optimizer.zero_grad()\n\u001b[32m     16\u001b[39m \u001b[38;5;66;03m# Forward pass\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m17\u001b[39m outputs = \u001b[43mmodel\u001b[49m\u001b[43m(\u001b[49m\u001b[43minputs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     19\u001b[39m \u001b[38;5;66;03m# Handle Inception-v3's special output format when aux_logits=True\u001b[39;00m\n\u001b[32m     20\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(outputs, \u001b[38;5;28mtuple\u001b[39m):\n\u001b[32m     21\u001b[39m     \u001b[38;5;66;03m# Inception-v3 with aux_logits returns (output, aux_output)\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\OneDrive\\Desktop\\Data Science\\New Example\\myvenv\\Lib\\site-packages\\torch\\nn\\modules\\module.py:1751\u001b[39m, in \u001b[36mModule._wrapped_call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1749\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._compiled_call_impl(*args, **kwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[32m   1750\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1751\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\OneDrive\\Desktop\\Data Science\\New Example\\myvenv\\Lib\\site-packages\\torch\\nn\\modules\\module.py:1762\u001b[39m, in \u001b[36mModule._call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1757\u001b[39m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[32m   1758\u001b[39m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[32m   1759\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m._backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_pre_hooks\n\u001b[32m   1760\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[32m   1761\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[32m-> \u001b[39m\u001b[32m1762\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1764\u001b[39m result = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1765\u001b[39m called_always_called_hooks = \u001b[38;5;28mset\u001b[39m()\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\OneDrive\\Desktop\\Data Science\\New Example\\myvenv\\Lib\\site-packages\\torchvision\\models\\inception.py:166\u001b[39m, in \u001b[36mInception3.forward\u001b[39m\u001b[34m(self, x)\u001b[39m\n\u001b[32m    164\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mforward\u001b[39m(\u001b[38;5;28mself\u001b[39m, x: Tensor) -> InceptionOutputs:\n\u001b[32m    165\u001b[39m     x = \u001b[38;5;28mself\u001b[39m._transform_input(x)\n\u001b[32m--> \u001b[39m\u001b[32m166\u001b[39m     x, aux = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_forward\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    167\u001b[39m     aux_defined = \u001b[38;5;28mself\u001b[39m.training \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m.aux_logits\n\u001b[32m    168\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m torch.jit.is_scripting():\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\OneDrive\\Desktop\\Data Science\\New Example\\myvenv\\Lib\\site-packages\\torchvision\\models\\inception.py:138\u001b[39m, in \u001b[36mInception3._forward\u001b[39m\u001b[34m(self, x)\u001b[39m\n\u001b[32m    136\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.AuxLogits \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    137\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.training:\n\u001b[32m--> \u001b[39m\u001b[32m138\u001b[39m         aux = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mAuxLogits\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    139\u001b[39m \u001b[38;5;66;03m# N x 768 x 17 x 17\u001b[39;00m\n\u001b[32m    140\u001b[39m x = \u001b[38;5;28mself\u001b[39m.Mixed_7a(x)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\OneDrive\\Desktop\\Data Science\\New Example\\myvenv\\Lib\\site-packages\\torch\\nn\\modules\\module.py:1751\u001b[39m, in \u001b[36mModule._wrapped_call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1749\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._compiled_call_impl(*args, **kwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[32m   1750\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1751\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\OneDrive\\Desktop\\Data Science\\New Example\\myvenv\\Lib\\site-packages\\torch\\nn\\modules\\module.py:1762\u001b[39m, in \u001b[36mModule._call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1757\u001b[39m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[32m   1758\u001b[39m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[32m   1759\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m._backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_pre_hooks\n\u001b[32m   1760\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[32m   1761\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[32m-> \u001b[39m\u001b[32m1762\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1764\u001b[39m result = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1765\u001b[39m called_always_called_hooks = \u001b[38;5;28mset\u001b[39m()\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\OneDrive\\Desktop\\Data Science\\New Example\\myvenv\\Lib\\site-packages\\torchvision\\models\\inception.py:386\u001b[39m, in \u001b[36mInceptionAux.forward\u001b[39m\u001b[34m(self, x)\u001b[39m\n\u001b[32m    384\u001b[39m x = \u001b[38;5;28mself\u001b[39m.conv0(x)\n\u001b[32m    385\u001b[39m \u001b[38;5;66;03m# N x 128 x 5 x 5\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m386\u001b[39m x = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mconv1\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    387\u001b[39m \u001b[38;5;66;03m# N x 768 x 1 x 1\u001b[39;00m\n\u001b[32m    388\u001b[39m \u001b[38;5;66;03m# Adaptive average pooling\u001b[39;00m\n\u001b[32m    389\u001b[39m x = F.adaptive_avg_pool2d(x, (\u001b[32m1\u001b[39m, \u001b[32m1\u001b[39m))\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\OneDrive\\Desktop\\Data Science\\New Example\\myvenv\\Lib\\site-packages\\torch\\nn\\modules\\module.py:1751\u001b[39m, in \u001b[36mModule._wrapped_call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1749\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._compiled_call_impl(*args, **kwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[32m   1750\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1751\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\OneDrive\\Desktop\\Data Science\\New Example\\myvenv\\Lib\\site-packages\\torch\\nn\\modules\\module.py:1762\u001b[39m, in \u001b[36mModule._call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1757\u001b[39m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[32m   1758\u001b[39m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[32m   1759\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m._backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_pre_hooks\n\u001b[32m   1760\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[32m   1761\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[32m-> \u001b[39m\u001b[32m1762\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1764\u001b[39m result = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1765\u001b[39m called_always_called_hooks = \u001b[38;5;28mset\u001b[39m()\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\OneDrive\\Desktop\\Data Science\\New Example\\myvenv\\Lib\\site-packages\\torchvision\\models\\inception.py:406\u001b[39m, in \u001b[36mBasicConv2d.forward\u001b[39m\u001b[34m(self, x)\u001b[39m\n\u001b[32m    404\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mforward\u001b[39m(\u001b[38;5;28mself\u001b[39m, x: Tensor) -> Tensor:\n\u001b[32m    405\u001b[39m     x = \u001b[38;5;28mself\u001b[39m.conv(x)\n\u001b[32m--> \u001b[39m\u001b[32m406\u001b[39m     x = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mbn\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    407\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m F.relu(x, inplace=\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\OneDrive\\Desktop\\Data Science\\New Example\\myvenv\\Lib\\site-packages\\torch\\nn\\modules\\module.py:1751\u001b[39m, in \u001b[36mModule._wrapped_call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1749\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._compiled_call_impl(*args, **kwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[32m   1750\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1751\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\OneDrive\\Desktop\\Data Science\\New Example\\myvenv\\Lib\\site-packages\\torch\\nn\\modules\\module.py:1762\u001b[39m, in \u001b[36mModule._call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1757\u001b[39m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[32m   1758\u001b[39m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[32m   1759\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m._backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_pre_hooks\n\u001b[32m   1760\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[32m   1761\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[32m-> \u001b[39m\u001b[32m1762\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1764\u001b[39m result = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1765\u001b[39m called_always_called_hooks = \u001b[38;5;28mset\u001b[39m()\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\OneDrive\\Desktop\\Data Science\\New Example\\myvenv\\Lib\\site-packages\\torch\\nn\\modules\\batchnorm.py:193\u001b[39m, in \u001b[36m_BatchNorm.forward\u001b[39m\u001b[34m(self, input)\u001b[39m\n\u001b[32m    186\u001b[39m     bn_training = (\u001b[38;5;28mself\u001b[39m.running_mean \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m) \u001b[38;5;129;01mand\u001b[39;00m (\u001b[38;5;28mself\u001b[39m.running_var \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m    188\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33mr\u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    189\u001b[39m \u001b[33;03mBuffers are only updated if they are to be tracked and we are in training mode. Thus they only need to be\u001b[39;00m\n\u001b[32m    190\u001b[39m \u001b[33;03mpassed when the update should occur (i.e. in training mode when they are tracked), or when buffer stats are\u001b[39;00m\n\u001b[32m    191\u001b[39m \u001b[33;03mused for normalization (i.e. in eval mode when buffers are not None).\u001b[39;00m\n\u001b[32m    192\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m193\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mF\u001b[49m\u001b[43m.\u001b[49m\u001b[43mbatch_norm\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    194\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    195\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;66;43;03m# If buffers are not to be tracked, ensure that they won't be updated\u001b[39;49;00m\n\u001b[32m    196\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrunning_mean\u001b[49m\n\u001b[32m    197\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mtraining\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mtrack_running_stats\u001b[49m\n\u001b[32m    198\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    199\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrunning_var\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mtraining\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mtrack_running_stats\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    200\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mweight\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    201\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mbias\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    202\u001b[39m \u001b[43m    \u001b[49m\u001b[43mbn_training\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    203\u001b[39m \u001b[43m    \u001b[49m\u001b[43mexponential_average_factor\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    204\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43meps\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    205\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\OneDrive\\Desktop\\Data Science\\New Example\\myvenv\\Lib\\site-packages\\torch\\nn\\functional.py:2820\u001b[39m, in \u001b[36mbatch_norm\u001b[39m\u001b[34m(input, running_mean, running_var, weight, bias, training, momentum, eps)\u001b[39m\n\u001b[32m   2807\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m handle_torch_function(\n\u001b[32m   2808\u001b[39m         batch_norm,\n\u001b[32m   2809\u001b[39m         (\u001b[38;5;28minput\u001b[39m, running_mean, running_var, weight, bias),\n\u001b[32m   (...)\u001b[39m\u001b[32m   2817\u001b[39m         eps=eps,\n\u001b[32m   2818\u001b[39m     )\n\u001b[32m   2819\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m training:\n\u001b[32m-> \u001b[39m\u001b[32m2820\u001b[39m     \u001b[43m_verify_batch_size\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msize\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   2822\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m torch.batch_norm(\n\u001b[32m   2823\u001b[39m     \u001b[38;5;28minput\u001b[39m,\n\u001b[32m   2824\u001b[39m     weight,\n\u001b[32m   (...)\u001b[39m\u001b[32m   2831\u001b[39m     torch.backends.cudnn.enabled,\n\u001b[32m   2832\u001b[39m )\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\OneDrive\\Desktop\\Data Science\\New Example\\myvenv\\Lib\\site-packages\\torch\\nn\\functional.py:2786\u001b[39m, in \u001b[36m_verify_batch_size\u001b[39m\u001b[34m(size)\u001b[39m\n\u001b[32m   2784\u001b[39m     size_prods *= size[i + \u001b[32m2\u001b[39m]\n\u001b[32m   2785\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m size_prods == \u001b[32m1\u001b[39m:\n\u001b[32m-> \u001b[39m\u001b[32m2786\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m   2787\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mExpected more than 1 value per channel when training, got input size \u001b[39m\u001b[38;5;132;01m{\u001b[39;00msize\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m   2788\u001b[39m     )\n", "\u001b[31mValueError\u001b[39m: Expected more than 1 value per channel when training, got input size torch.Size([1, 768, 1, 1])"]}], "source": ["# Run the training\n", "inception_v3_results = train_inception_v3_on_folds(num_epochs=10, batch_size=16)"]}], "metadata": {"kernelspec": {"display_name": "myvenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}