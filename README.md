# Kidney CT Scan Classifier Web Application

This web application allows users to upload CT scan images and classifies them as <PERSON>, <PERSON><PERSON>, <PERSON>, or <PERSON><PERSON> using a trained EfficientNet model.

## Setup Instructions

### Prerequisites
- Python 3.8 or higher
- PyTorch and TorchVision
- Flask
- Your trained EfficientNet model (`efficientnet_final.pth`)

### Installation

1. Clone this repository or download the files.

2. Create a virtual environment (recommended):
   ```
   python -m venv venv
   ```

3. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - macOS/Linux: `source venv/bin/activate`

4. Install the required packages:
   ```
   pip install -r requirements.txt
   ```

5. Place your trained model file (`efficientnet_final.pth`) in the root directory of the project.

### Running the Application

1. Start the Flask server:
   ```
   python app.py
   ```

2. Open a web browser and navigate to:
   ```
   http://127.0.0.1:5000/
   ```

3. Upload a CT scan image and click "Classify Image" to see the results.

## Project Structure

- `app.py`: The main Flask application
- `templates/`: HTML templates for the web interface
  - `index.html`: Upload page
  - `result.html`: Results page
- `static/uploads/`: Directory where uploaded images are stored
- `efficientnet_final.pth`: Your trained model file

## Features

- Image upload and preview
- Classification of CT scans into four categories
- Visualization of classification probabilities
- Responsive design for desktop and mobile devices

## Customization

- Update the `class_names` list in `app.py` if your model uses different classes
- Modify the transformation parameters to match your training configuration
- Customize the HTML templates to change the appearance of the web interface

## Troubleshooting

- If you encounter CUDA errors, try setting `device = torch.device("cpu")` in `app.py`
- Make sure the image transformations match exactly what you used during training
- Check that your model file is correctly named and placed in the root directory
