# Add this cell to final_5fold_cv.ipynb to generate a confusion matrix for EfficientNet

# Cell 1: Import necessary libraries for confusion matrix
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report

# Cell 2: Function to generate predictions and true labels
def get_predictions(model, data_loader, device):
    model.eval()
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for inputs, labels in data_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            
            # Forward pass
            outputs = model(inputs)
            _, predicted = torch.max(outputs, 1)
            
            # Collect predictions and labels
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    return np.array(all_preds), np.array(all_labels)

# Cell 3: Load the EfficientNet model and generate predictions
# Load the EfficientNet model
model = initialize_model('efficientnet_b0')
model.to(device)

# Load the trained weights
model_path = 'efficientnet_b0_fold1.pth'  # You can change this to any fold
if os.path.exists(model_path):
    model.load_state_dict(torch.load(model_path, map_location=device))
    print(f"Loaded EfficientNet weights from {model_path}")
else:
    print(f"Warning: EfficientNet weights not found at {model_path}")

# Set model to evaluation mode
model.eval()

# Get predictions and true labels
y_pred, y_true = get_predictions(model, test_loader, device)

# Cell 4: Generate and display confusion matrix
# Generate confusion matrix
cm = confusion_matrix(y_true, y_pred)
print("Confusion Matrix:")
print(cm)

# Generate classification report
report = classification_report(y_true, y_pred, target_names=class_labels)
print("\nClassification Report:")
print(report)

# Function to plot confusion matrix
def plot_confusion_matrix(cm, classes, normalize=False, title='Confusion Matrix', cmap=plt.cm.Blues):
    plt.figure(figsize=(10, 8))
    
    if normalize:
        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        fmt = '.2f'
        title = 'Normalized ' + title
    else:
        fmt = 'd'
    
    # Create heatmap
    sns.heatmap(cm, annot=True, fmt=fmt, cmap=cmap, 
                xticklabels=classes, yticklabels=classes, 
                square=True, cbar=True)
    
    plt.title(title, fontsize=16)
    plt.ylabel('True Label', fontsize=12)
    plt.xlabel('Predicted Label', fontsize=12)
    plt.tight_layout()
    plt.show()

# Plot raw counts
plot_confusion_matrix(cm, class_labels, normalize=False, title='EfficientNet Confusion Matrix')

# Plot normalized (percentage)
plot_confusion_matrix(cm, class_labels, normalize=True, title='EfficientNet Confusion Matrix (Normalized)')

# Cell 5: Analyze per-class metrics
# Calculate per-class accuracy
class_accuracy = np.diag(cm) / np.sum(cm, axis=1)

# Plot per-class accuracy
plt.figure(figsize=(10, 6))
bars = plt.bar(class_labels, class_accuracy * 100)

# Add value labels on top of bars
for bar in bars:
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + 1,
             f'{height:.1f}%', ha='center', va='bottom', fontsize=12)

plt.title('EfficientNet Per-Class Accuracy', fontsize=16)
plt.ylabel('Accuracy (%)', fontsize=12)
plt.ylim(0, 105)  # Set y-axis limit to accommodate the text
plt.grid(axis='y', linestyle='--', alpha=0.7)
plt.show()

# Identify the most common misclassifications
misclassifications = []
for i in range(len(class_labels)):
    for j in range(len(class_labels)):
        if i != j and cm[i, j] > 0:
            misclassifications.append({
                'true': class_labels[i],
                'predicted': class_labels[j],
                'count': cm[i, j],
                'percentage': cm[i, j] / np.sum(cm[i, :]) * 100
            })

# Sort by count (highest first)
misclassifications.sort(key=lambda x: x['count'], reverse=True)

# Display top misclassifications
print("\nTop Misclassifications:")
for i, misclass in enumerate(misclassifications[:5]):
    print(f"{i+1}. True: {misclass['true']}, Predicted: {misclass['predicted']}, "
          f"Count: {misclass['count']}, Percentage: {misclass['percentage']:.1f}%")
