<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Classification Result</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 50px;
        }
        .container {
            max-width: 800px;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .result-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .image-container {
            margin-bottom: 20px;
            text-align: center;
        }
        .image-container img {
            max-width: 100%;
            max-height: 300px;
            border-radius: 5px;
        }
        .prediction {
            font-size: 24px;
            font-weight: bold;
            margin: 20px 0;
            padding: 10px 20px;
            border-radius: 5px;
        }
        .Normal {
            background-color: #d4edda;
            color: #155724;
        }
        .Cyst {
            background-color: #cce5ff;
            color: #004085;
        }
        .Stone {
            background-color: #fff3cd;
            color: #856404;
        }
        .Tumor {
            background-color: #f8d7da;
            color: #721c24;
        }
        .chart-container {
            width: 100%;
            max-width: 500px;
            margin: 20px auto;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 0.9em;
            color: #6c757d;
        }
        .btn-back {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Classification Result</h1>
        </div>

        <div class="result-container">
            <div class="image-container">
                <img src="{{ url_for('display_image', filename=filename) }}" alt="Uploaded CT Scan">
            </div>

            <div class="prediction {{ prediction }}">
                Prediction: {{ prediction }}
            </div>

            <div class="chart-container">
                <canvas id="probabilityChart"></canvas>
            </div>

            <div class="probability-details mt-4">
                <h4>Confidence Scores:</h4>
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Class</th>
                            <th>Probability</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in probabilities %}
                        <tr>
                            <td>{{ item.class }}</td>
                            <td>{{ "%.2f"|format(item.prob) }}%</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <a href="/" class="btn btn-primary btn-back">Classify Another Image</a>
        </div>

        <div class="footer">
            <p>Developed by Your Group Name | Data Science Project</p>
        </div>
    </div>

    <script>
        // Create chart for probabilities
        var ctx = document.getElementById('probabilityChart').getContext('2d');
        var chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [{% for item in probabilities %}'{{ item.class }}',{% endfor %}],
                datasets: [{
                    label: 'Probability (%)',
                    data: [{% for item in probabilities %}{{ item.prob }},{% endfor %}],
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.6)',
                        'rgba(54, 162, 235, 0.6)',
                        'rgba(255, 206, 86, 0.6)',
                        'rgba(255, 99, 132, 0.6)'
                    ],
                    borderColor: [
                        'rgba(75, 192, 192, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Classification Probabilities'
                    }
                }
            }
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
