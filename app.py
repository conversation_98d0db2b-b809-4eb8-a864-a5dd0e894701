import os
import torch
import torch.nn.functional as F
from torchvision import transforms, models
import torch.nn as nn
from PIL import Image
from flask import Flask, render_template, request, redirect, url_for, flash
from werkzeug.utils import secure_filename

# Create Flask app
app = Flask(__name__)
app.secret_key = 'kidney_ct_classifier'  # Needed for flashing messages

# Configure upload folder
UPLOAD_FOLDER = 'static/uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# Ensure upload directory exists
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Device configuration
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# Define the class names
class_names = ['Cyst', 'Normal', 'Stone', 'Tumor']

# Image transformation - must match what you used during training
transform = transforms.Compose([
    transforms.Resize((128, 128)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

# Load the model
def load_model():
    # Load the EfficientNet model
    model = models.efficientnet_b0(weights=None)
    
    # Modify the classifier for our number of classes
    num_ftrs = model.classifier[1].in_features
    model.classifier = nn.Sequential(
        nn.Dropout(p=0.2, inplace=True),
        nn.Linear(in_features=num_ftrs, out_features=len(class_names))
    )
    
    # Load the trained weights
    model.load_state_dict(torch.load('efficientnet_final.pth', map_location=device))
    model.to(device)
    model.eval()
    return model

# Load model at startup
model = load_model()

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/', methods=['GET', 'POST'])
def upload_file():
    if request.method == 'POST':
        # Check if the post request has the file part
        if 'file' not in request.files:
            flash('No file part')
            return redirect(request.url)
        
        file = request.files['file']
        
        # If user does not select file, browser also submits an empty part without filename
        if file.filename == '':
            flash('No selected file')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            # Process the image and get prediction
            prediction, probabilities = predict_image(filepath)
            
            # Return results page
            return render_template('result.html', 
                                  filename=filename, 
                                  prediction=prediction,
                                  probabilities=probabilities,
                                  class_names=class_names)
    
    # GET request - show upload form
    return render_template('index.html')

def predict_image(image_path):
    # Open image
    img = Image.open(image_path).convert('RGB')
    
    # Apply transformations
    img_tensor = transform(img).unsqueeze(0).to(device)
    
    # Get prediction
    with torch.no_grad():
        outputs = model(img_tensor)
        probabilities = F.softmax(outputs, dim=1)[0]
        _, predicted = torch.max(outputs, 1)
        prediction = class_names[predicted.item()]
    
    # Convert probabilities to percentages
    probs = [float(p) * 100 for p in probabilities.cpu().numpy()]
    
    # Create a list of (class_name, probability) tuples
    prob_dict = [{'class': class_names[i], 'prob': probs[i]} for i in range(len(class_names))]
    
    # Sort by probability (highest first)
    prob_dict = sorted(prob_dict, key=lambda x: x['prob'], reverse=True)
    
    return prediction, prob_dict

@app.route('/display/<filename>')
def display_image(filename):
    return redirect(url_for('static', filename='uploads/' + filename))

if __name__ == '__main__':
    app.run(debug=True)
