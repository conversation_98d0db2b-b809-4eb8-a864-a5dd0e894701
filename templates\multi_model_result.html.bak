<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Classification Results</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            padding-top: 2rem;
            background-color: #f8f9fa;
        }
        .result-container {
            max-width: 900px;
            margin: 0 auto;
            background-color: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .header h1 {
            color: #343a40;
        }
        .image-container {
            text-align: center;
            margin-bottom: 2rem;
        }
        .image-container img {
            max-width: 100%;
            max-height: 300px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .model-result {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .model-result h3 {
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #dee2e6;
        }
        .prediction {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 1rem;
            padding: 0.5rem;
            border-radius: 4px;
            display: inline-block;
        }
        .chart-container {
            height: 250px;
            margin-top: 1rem;
        }
        .back-btn {
            display: block;
            width: 100%;
            padding: 1rem;
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1.1rem;
            text-align: center;
            text-decoration: none;
            transition: background-color 0.3s;
        }
        .back-btn:hover {
            background-color: #5a6268;
            text-decoration: none;
            color: white;
        }
        .efficientnet-color { background-color: #e3f2fd; }
        .resnet-color { background-color: #e8f5e9; }
        .googlenet-color { background-color: #fff3e0; }
        .cyst-color { background-color: #bbdefb; }
        .normal-color { background-color: #c8e6c9; }
        .stone-color { background-color: #ffecb3; }
        .tumor-color { background-color: #ffccbc; }
    </style>
</head>
<body>
    <div class="container">
        <div class="result-container">
            <div class="header">
                <h1>Classification Results</h1>
                <p>Results from three different models</p>
            </div>

            <div class="image-container">
                <img src="{{ url_for('display_image', filename=filename) }}" alt="Uploaded Image">
            </div>

            <div class="row">
                <!-- EfficientNet Results -->
                <div class="col-md-4">
                    <div class="model-result efficientnet-color">
                        <h3>EfficientNet-B0</h3>
                        <div class="prediction 
                            {% if results.efficientnet.prediction == 'Cyst' %}cyst-color
                            {% elif results.efficientnet.prediction == 'Normal' %}normal-color
                            {% elif results.efficientnet.prediction == 'Stone' %}stone-color
                            {% elif results.efficientnet.prediction == 'Tumor' %}tumor-color{% endif %}">
                            {{ results.efficientnet.prediction }}
                        </div>
                        <div class="chart-container">
                            <canvas id="efficientnetChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- ResNet50 Results -->
                <div class="col-md-4">
                    <div class="model-result resnet-color">
                        <h3>ResNet50</h3>
                        <div class="prediction 
                            {% if results.resnet50.prediction == 'Cyst' %}cyst-color
                            {% elif results.resnet50.prediction == 'Normal' %}normal-color
                            {% elif results.resnet50.prediction == 'Stone' %}stone-color
                            {% elif results.resnet50.prediction == 'Tumor' %}tumor-color{% endif %}">
                            {{ results.resnet50.prediction }}
                        </div>
                        <div class="chart-container">
                            <canvas id="resnetChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- GoogLeNet Results -->
                <div class="col-md-4">
                    <div class="model-result googlenet-color">
                        <h3>GoogLeNet</h3>
                        <div class="prediction 
                            {% if results.googlenet.prediction == 'Cyst' %}cyst-color
                            {% elif results.googlenet.prediction == 'Normal' %}normal-color
                            {% elif results.googlenet.prediction == 'Stone' %}stone-color
                            {% elif results.googlenet.prediction == 'Tumor' %}tumor-color{% endif %}">
                            {{ results.googlenet.prediction }}
                        </div>
                        <div class="chart-container">
                            <canvas id="googlenetChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <a href="/" class="back-btn mt-4">Classify Another Image</a>
        </div>
    </div>

    <script>
        // Chart colors
        const chartColors = {
            'Cyst': 'rgba(33, 150, 243, 0.7)',
            'Normal': 'rgba(76, 175, 80, 0.7)',
            'Stone': 'rgba(255, 193, 7, 0.7)',
            'Tumor': 'rgba(255, 87, 34, 0.7)'
        };

        // Create EfficientNet chart
        const efficientnetCtx = document.getElementById('efficientnetChart').getContext('2d');
        new Chart(efficientnetCtx, {
            type: 'bar',
            data: {
                labels: [
                    {% for prob in results.efficientnet.probabilities %}
                        '{{ prob.class }}',
                    {% endfor %}
                ],
                datasets: [{
                    label: 'Probability (%)',
                    data: [
                        {% for prob in results.efficientnet.probabilities %}
                            {{ prob.prob }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        {% for prob in results.efficientnet.probabilities %}
                            chartColors['{{ prob.class }}'],
                        {% endfor %}
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        // Create ResNet chart
        const resnetCtx = document.getElementById('resnetChart').getContext('2d');
        new Chart(resnetCtx, {
            type: 'bar',
            data: {
                labels: [
                    {% for prob in results.resnet50.probabilities %}
                        '{{ prob.class }}',
                    {% endfor %}
                ],
                datasets: [{
                    label: 'Probability (%)',
                    data: [
                        {% for prob in results.resnet50.probabilities %}
                            {{ prob.prob }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        {% for prob in results.resnet50.probabilities %}
                            chartColors['{{ prob.class }}'],
                        {% endfor %}
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        // Create GoogLeNet chart
        const googlenetCtx = document.getElementById('googlenetChart').getContext('2d');
        new Chart(googlenetCtx, {
            type: 'bar',
            data: {
                labels: [
                    {% for prob in results.googlenet.probabilities %}
                        '{{ prob.class }}',
                    {% endfor %}
                ],
                datasets: [{
                    label: 'Probability (%)',
                    data: [
                        {% for prob in results.googlenet.probabilities %}
                            {{ prob.prob }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        {% for prob in results.googlenet.probabilities %}
                            chartColors['{{ prob.class }}'],
                        {% endfor %}
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    </script>
</body>
</html>
