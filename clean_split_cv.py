"""
Clean 80-20 Split with 5-Fold Cross-Validation

This script implements a clean 80-20 train-test split followed by 5-fold cross-validation 
on the training data only. This approach prevents data leakage and ensures proper 
evaluation of model performance.
"""

# Import necessary libraries
import os
import re
import random
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import warnings
warnings.filterwarnings('ignore')

# Import PyTorch components
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, Subset
from torchvision import transforms, models

# Import scikit-learn for splitting
from sklearn.model_selection import train_test_split, KFold

# Set random seeds for reproducibility
random.seed(42)
np.random.seed(42)
torch.manual_seed(42)
if torch.cuda.is_available():
    torch.cuda.manual_seed_all(42)
    torch.backends.cudnn.benchmark = True  # Speed up training

# Device configuration
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# Define Dataset Path
DATASET_PATH = "C:/Users/<USER>/OneDrive/Desktop/Data Science/New Example/CT Scan/CT-KIDNEY-DATASET-Normal-Cyst-Tumor-Stone/Resized"

# Get class labels (folder names)
class_labels = sorted([d for d in os.listdir(DATASET_PATH) if os.path.isdir(os.path.join(DATASET_PATH, d))])

# Print basic info
print(f"Found {len(class_labels)} classes: {class_labels}")

# Dictionary to store image file paths
image_files = {label: [] for label in class_labels}

# Read images from each class
for label in class_labels:
    class_dir = os.path.join(DATASET_PATH, label)
    image_files[label] = [os.path.join(class_dir, img) for img in os.listdir(class_dir) if img.endswith(('.png', '.jpg', '.jpeg'))]

# Print number of images per class
for label, files in image_files.items():
    print(f"Class '{label}': {len(files)} images")

# Function to extract patient ID from filename
def get_patient_id(filename):
    # Extract patient ID from filename using regex
    match = re.search(r'(\d+)', os.path.basename(filename))
    return match.group(1) if match else os.path.basename(filename)

# Custom dataset class with patient ID tracking
class KidneyCTDataset(Dataset):
    def __init__(self, root_dir, transform=None):
        self.root_dir = root_dir
        self.transform = transform
        self.classes = sorted([d for d in os.listdir(root_dir) if os.path.isdir(os.path.join(root_dir, d))])
        self.class_to_idx = {cls: i for i, cls in enumerate(self.classes)}
        
        self.samples = []
        self.patient_to_samples = {}
        
        for class_name in self.classes:
            class_dir = os.path.join(root_dir, class_name)
            for img_name in os.listdir(class_dir):
                if img_name.endswith(('.png', '.jpg', '.jpeg')):
                    img_path = os.path.join(class_dir, img_name)
                    patient_id = get_patient_id(img_path)
                    
                    self.samples.append((img_path, self.class_to_idx[class_name]))
                    
                    if patient_id not in self.patient_to_samples:
                        self.patient_to_samples[patient_id] = []
                    self.patient_to_samples[patient_id].append(len(self.samples) - 1)
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        img_path, label = self.samples[idx]
        image = Image.open(img_path).convert('RGB')
        
        if self.transform:
            image = self.transform(image)
            
        return image, label

# Define transformations
transform = transforms.Compose([
    transforms.Resize((128, 128)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

# Load dataset
dataset = KidneyCTDataset(root_dir=DATASET_PATH, transform=transform)

# Get all patient IDs
patient_ids = list(dataset.patient_to_samples.keys())
print(f"Total patients: {len(patient_ids)}")

# Print class distribution
class_counts = {}
for _, label in dataset.samples:
    class_name = dataset.classes[label]
    if class_name not in class_counts:
        class_counts[class_name] = 0
    class_counts[class_name] += 1

print("\nClass distribution:")
for class_name, count in class_counts.items():
    print(f"  {class_name}: {count} images")

# Visualize class distribution
plt.figure(figsize=(10, 6))
plt.bar(class_counts.keys(), class_counts.values())
plt.title('Class Distribution in Dataset')
plt.xlabel('Class')
plt.ylabel('Number of Images')
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# Step 1: Perform a clean 80-20 split by patient ID
# This ensures no patient data leakage between train and test sets
train_patients, test_patients = train_test_split(
    patient_ids, 
    test_size=0.2, 
    random_state=42
)

print(f"Train-Test Split:")
print(f"  Train: {len(train_patients)} patients")
print(f"  Test: {len(test_patients)} patients")

# Get sample indices for each split
train_indices = [idx for patient in train_patients for idx in dataset.patient_to_samples[patient]]
test_indices = [idx for patient in test_patients for idx in dataset.patient_to_samples[patient]]

print(f"  Train: {len(train_indices)} images")
print(f"  Test: {len(test_indices)} images")

# Create datasets
train_full_dataset = Subset(dataset, train_indices)
test_dataset = Subset(dataset, test_indices)

# Analyze class distribution in train and test sets
def get_class_distribution(subset_indices, dataset):
    class_counts = {}
    for idx in subset_indices:
        _, label = dataset.samples[idx]
        class_name = dataset.classes[label]
        if class_name not in class_counts:
            class_counts[class_name] = 0
        class_counts[class_name] += 1
    return class_counts

train_class_counts = get_class_distribution(train_indices, dataset)
test_class_counts = get_class_distribution(test_indices, dataset)

print("\nTrain set class distribution:")
for class_name, count in train_class_counts.items():
    print(f"  {class_name}: {count} images")

print("\nTest set class distribution:")
for class_name, count in test_class_counts.items():
    print(f"  {class_name}: {count} images")

# Visualize class distribution in train and test sets
plt.figure(figsize=(12, 6))

plt.subplot(1, 2, 1)
plt.bar(train_class_counts.keys(), train_class_counts.values())
plt.title('Train Set Class Distribution')
plt.xlabel('Class')
plt.ylabel('Number of Images')
plt.xticks(rotation=45)

plt.subplot(1, 2, 2)
plt.bar(test_class_counts.keys(), test_class_counts.values())
plt.title('Test Set Class Distribution')
plt.xlabel('Class')
plt.ylabel('Number of Images')
plt.xticks(rotation=45)

plt.tight_layout()
plt.show()

# Step 2: Divide the training data into 5 folds for cross-validation
# Initialize KFold
kf = KFold(n_splits=5, shuffle=True, random_state=42)

# Create a mapping from train_indices to their position in train_indices
train_idx_map = {idx: i for i, idx in enumerate(train_indices)}

# Create a list to store fold information
cv_folds = []

# Split train_patients into 5 folds
for fold_idx, (fold_train_patient_indices, fold_val_patient_indices) in enumerate(kf.split(train_patients)):
    # Get patient IDs for this fold
    fold_train_patients = [train_patients[i] for i in fold_train_patient_indices]
    fold_val_patients = [train_patients[i] for i in fold_val_patient_indices]
    
    # Get sample indices for each split
    fold_train_indices = [idx for patient in fold_train_patients for idx in dataset.patient_to_samples[patient]]
    fold_val_indices = [idx for patient in fold_val_patients for idx in dataset.patient_to_samples[patient]]
    
    # Map these indices to positions in train_indices
    fold_train_positions = [train_idx_map[idx] for idx in fold_train_indices if idx in train_idx_map]
    fold_val_positions = [train_idx_map[idx] for idx in fold_val_indices if idx in train_idx_map]
    
    # Store fold information
    cv_folds.append({
        'train_patients': fold_train_patients,
        'val_patients': fold_val_patients,
        'train_positions': fold_train_positions,
        'val_positions': fold_val_positions
    })
    
    print(f"Fold {fold_idx+1}:")
    print(f"  Train: {len(fold_train_patients)} patients, {len(fold_train_positions)} images")
    print(f"  Validation: {len(fold_val_patients)} patients, {len(fold_val_positions)} images")

# Create DataLoaders for each fold
BATCH_SIZE = 8  # Small batch size for better memory usage

# Function to create data loaders for a specific fold
def create_fold_dataloaders(fold_idx):
    fold = cv_folds[fold_idx]
    
    # Create subsets for this fold
    fold_train_dataset = Subset(train_full_dataset, fold['train_positions'])
    fold_val_dataset = Subset(train_full_dataset, fold['val_positions'])
    
    # Create data loaders
    fold_train_loader = DataLoader(
        fold_train_dataset, 
        batch_size=BATCH_SIZE, 
        shuffle=True, 
        num_workers=0,
        pin_memory=True
    )
    
    fold_val_loader = DataLoader(
        fold_val_dataset, 
        batch_size=BATCH_SIZE, 
        shuffle=False, 
        num_workers=0,
        pin_memory=True
    )
    
    return fold_train_loader, fold_val_loader

# Create test data loader (same for all folds)
test_loader = DataLoader(
    test_dataset, 
    batch_size=BATCH_SIZE, 
    shuffle=False, 
    num_workers=0,
    pin_memory=True
)

# Example: Create data loaders for the first fold
current_fold = 0
train_loader, val_loader = create_fold_dataloaders(current_fold)

print(f"Created data loaders for fold {current_fold+1}:")
print(f"  Train: {len(train_loader.dataset)} images")
print(f"  Validation: {len(val_loader.dataset)} images")
print(f"  Test: {len(test_loader.dataset)} images")

# Visualize the data split and cross-validation structure
plt.figure(figsize=(12, 8))

# Main train-test split
plt.subplot(2, 1, 1)
plt.bar(['Train', 'Test'], [len(train_indices), len(test_indices)])
plt.title('Initial 80-20 Train-Test Split')
plt.ylabel('Number of Images')

# Cross-validation folds
plt.subplot(2, 1, 2)
train_sizes = [len(fold['train_positions']) for fold in cv_folds]
val_sizes = [len(fold['val_positions']) for fold in cv_folds]

x = np.arange(5)
width = 0.35
plt.bar(x - width/2, train_sizes, width, label='Train')
plt.bar(x + width/2, val_sizes, width, label='Validation')
plt.xlabel('Fold')
plt.ylabel('Number of Images')
plt.title('5-Fold Cross-Validation on Training Data')
plt.xticks(x, [f'Fold {i+1}' for i in range(5)])
plt.legend()

plt.tight_layout()
plt.show()

print("\nSummary of the clean 80-20 split with 5-fold cross-validation approach:")
print("1. We first split the dataset into 80% training and 20% test sets by patient ID")
print("   - This ensures no patient data leakage between train and test sets")
print("2. We then divide the 80% training data into 5 folds for cross-validation")
print("   - Each fold uses 4/5 of the training data for training and 1/5 for validation")
print("   - Again, we split by patient ID to prevent data leakage")
print("3. The test set is completely held out and only used for final evaluation")
print("4. This approach provides a robust evaluation of model performance while preventing data leakage")
