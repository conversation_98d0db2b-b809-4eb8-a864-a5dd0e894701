# Kidney CT Scan Classifier - Jupyter Notebook Web App

This project provides a web interface for classifying kidney CT scans using a trained EfficientNet model, all within a Jupyter Notebook.

## Setup Instructions

### Prerequisites
- Python 3.8 or higher
- Jupyter Notebook or JupyterLab
- PyTorch and TorchVision
- Your trained EfficientNet model (`efficientnet_final.pth`)

### Installation

1. Clone this repository or download the files.

2. Create a virtual environment (recommended):
   ```
   python -m venv venv
   ```

3. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - macOS/Linux: `source venv/bin/activate`

4. Install the required packages:
   ```
   pip install -r requirements_notebook.txt
   ```

5. Place your trained model file (`efficientnet_final.pth`) in the same directory as the notebook.

### Running the Application

#### Option 1: Run as a Jupyter Notebook
1. Start Jupyter Notebook:
   ```
   jupyter notebook
   ```

2. Open `ct_scan_classifier_app.ipynb` and run all cells.

3. Interact with the web interface directly in the notebook.

#### Option 2: Run as a Standalone Web App with Voilà
1. Install Voilà if you haven't already:
   ```
   pip install voila
   ```

2. Run the notebook as a web application:
   ```
   voila ct_scan_classifier_app.ipynb
   ```

3. Your default web browser will open with the application running.

## Features

- Upload and preview CT scan images
- Classify images as Normal, Cyst, Stone, or Tumor
- View classification probabilities as a chart and table
- Interactive interface built entirely in a Jupyter Notebook

## Customization

- Update the `class_names` list if your model uses different classes
- Modify the transformation parameters to match your training configuration
- Adjust the visualization settings in the notebook cells

## Troubleshooting

- If you encounter CUDA errors, try setting `device = torch.device("cpu")` in the notebook
- Make sure the image transformations match exactly what you used during training
- Check that your model file is correctly named and placed in the same directory as the notebook

## Sharing the Application

To share this application with others:
1. Set up a Jupyter Hub or Binder instance
2. Deploy using Voilà as a standalone web service
3. Convert to a desktop application using tools like Electron
