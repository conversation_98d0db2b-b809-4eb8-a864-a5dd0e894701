<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Model Kidney CT Classifier</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding-top: 2rem;
            background-color: #f8f9fa;
        }
        .upload-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .header h1 {
            color: #343a40;
        }
        .header p {
            color: #6c757d;
        }
        .upload-btn {
            display: block;
            width: 100%;
            padding: 1rem;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1.1rem;
            transition: background-color 0.3s;
        }
        .upload-btn:hover {
            background-color: #0069d9;
        }
        .preview-container {
            margin-top: 1.5rem;
            text-align: center;
        }
        #image-preview {
            max-width: 100%;
            max-height: 300px;
            display: none;
            margin: 0 auto;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .model-info {
            margin-top: 2rem;
            padding: 1rem;
            background-color: #f1f8ff;
            border-radius: 5px;
        }
        .model-info h3 {
            color: #0056b3;
            font-size: 1.2rem;
        }
        .model-info ul {
            padding-left: 1.5rem;
        }
        .filename-display {
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="upload-container">
            <div class="header">
                <h1>Multi-Model Kidney CT Classifier</h1>
                <p>Upload a kidney CT scan image to get classifications from four different models</p>
            </div>

            {% with messages = get_flashed_messages() %}
                {% if messages %}
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            {% for message in messages %}
                                <li>{{ message }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}
            {% endwith %}

            <form action="/predict" method="post" enctype="multipart/form-data" id="upload-form">
                <div class="mb-3">
                    <label for="file" class="form-label">Choose an image:</label>
                    <input type="file" class="form-control" id="file" name="file" accept=".jpg, .jpeg, .png" required>
                </div>
                <button type="submit" class="upload-btn">Classify Image</button>
            </form>

            <div class="preview-container">
                <img id="image-preview" alt="Image Preview">
                <div id="filename-display" class="filename-display"></div>
            </div>

            <div class="model-info">
                <h3>About the Models</h3>
                <ul>
                    <li><strong>EfficientNet-B0:</strong> A lightweight model with excellent accuracy-to-parameter ratio</li>
                    <li><strong>ResNet50:</strong> A deep residual network with 50 layers</li>
                    <li><strong>GoogLeNet:</strong> Also known as Inception v1, uses inception modules</li>
                    <li><strong>RegNetY-8GF:</strong> A systematically designed CNN architecture from Facebook AI Research</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Preview the selected image
        document.getElementById('file').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('image-preview');
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                    
                    // Display the filename
                    document.getElementById('filename-display').textContent = 'Selected file: ' + file.name;
                }
                reader.readAsDataURL(file);
            }
        });
    </script>
</body>
</html>
