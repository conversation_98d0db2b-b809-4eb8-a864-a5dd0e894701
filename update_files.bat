@echo off
echo Updating multi-model app files...

echo Backing up original files...
copy multi_model_app.py multi_model_app.py.bak
copy templates\multi_model_index.html templates\multi_model_index.html.bak
copy templates\multi_model_result.html templates\multi_model_result.html.bak

echo Replacing with updated files...
copy multi_model_app_updated.py multi_model_app.py
copy templates\multi_model_index_updated.html templates\multi_model_index.html
copy templates\multi_model_result_updated.html templates\multi_model_result.html

echo Update complete!
echo Original files have been backed up with .bak extension
