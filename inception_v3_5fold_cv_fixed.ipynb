{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Inception-v3: 5-Fold Cross-Validation with Confusion Matrix per Fold\n", "\n", "This notebook implements a clean 80-20 train-test split followed by 5-fold cross-validation with **no data reuse**.\n", "Each fold contains a completely separate subset of the training data.\n", "A confusion matrix is generated after each fold, and final average train and test accuracies are reported.\n", "\n", "**Model: Inception-v3**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import os\n", "import re\n", "import random\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from PIL import Image\n", "from tqdm.notebook import tqdm\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Import PyTorch components\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch.utils.data import Dataset, DataLoader, Subset\n", "from torchvision import transforms, models\n", "\n", "# Import scikit-learn for splitting and metrics\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import confusion_matrix, classification_report\n", "import seaborn as sns\n", "\n", "# Set random seeds for reproducibility\n", "random.seed(42)\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "if torch.cuda.is_available():\n", "    torch.cuda.manual_seed_all(42)\n", "    torch.backends.cudnn.benchmark = True  # Speed up training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Device configuration\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define Dataset Path\n", "DATASET_PATH = \"C:/Users/<USER>/OneDrive/Desktop/Data Science/New Example/CT Scan/CT-KIDNEY-DATASET-Normal-Cyst-<PERSON><PERSON>-Stone/Resized\"\n", "\n", "# Get class labels (folder names)\n", "class_labels = sorted([d for d in os.listdir(DATASET_PATH) if os.path.isdir(os.path.join(DATASET_PATH, d))])\n", "\n", "# Print basic info\n", "print(f\"Found {len(class_labels)} classes: {class_labels}\")\n", "\n", "# Dictionary to store image file paths\n", "image_files = {label: [] for label in class_labels}\n", "\n", "# Read images from each class\n", "for label in class_labels:\n", "    class_dir = os.path.join(DATASET_PATH, label)\n", "    image_files[label] = [os.path.join(class_dir, img) for img in os.listdir(class_dir) if img.endswith(('.png', '.jpg', '.jpeg'))]\n", "\n", "# Print number of images per class\n", "total_images = 0\n", "for label, files in image_files.items():\n", "    print(f\"Class '{label}': {len(files)} images\")\n", "    total_images += len(files)\n", "    \n", "print(f\"\\nTotal images in dataset: {total_images}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function to extract patient ID from filename\n", "def get_patient_id(filename):\n", "    # Extract patient ID from filename using regex\n", "    match = re.search(r'(\\d+)', os.path.basename(filename))\n", "    return match.group(1) if match else os.path.basename(filename)\n", "\n", "# Custom dataset class with patient ID tracking\n", "class KidneyCTDataset(Dataset):\n", "    def __init__(self, root_dir, transform=None):\n", "        self.root_dir = root_dir\n", "        self.transform = transform\n", "        self.classes = sorted([d for d in os.listdir(root_dir) if os.path.isdir(os.path.join(root_dir, d))])\n", "        self.class_to_idx = {cls: i for i, cls in enumerate(self.classes)}\n", "        \n", "        self.samples = []\n", "        self.patient_to_samples = {}\n", "        \n", "        # Load all image paths and organize by patient ID\n", "        for class_name in self.classes:\n", "            class_dir = os.path.join(root_dir, class_name)\n", "            for img_name in os.listdir(class_dir):\n", "                if img_name.endswith(('.png', '.jpg', '.jpeg')):\n", "                    img_path = os.path.join(class_dir, img_name)\n", "                    patient_id = get_patient_id(img_path)\n", "                    \n", "                    # Add to samples list\n", "                    idx = len(self.samples)\n", "                    self.samples.append((img_path, self.class_to_idx[class_name]))\n", "                    \n", "                    # Add to patient mapping\n", "                    if patient_id not in self.patient_to_samples:\n", "                        self.patient_to_samples[patient_id] = []\n", "                    self.patient_to_samples[patient_id].append(idx)\n", "    \n", "    def __len__(self):\n", "        return len(self.samples)\n", "    \n", "    def __getitem__(self, idx):\n", "        img_path, label = self.samples[idx]\n", "        \n", "        # Load image\n", "        image = Image.open(img_path).convert('RGB')\n", "        \n", "        # Apply transformations\n", "        if self.transform:\n", "            image = self.transform(image)\n", "        \n", "        return image, label"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define transformations\n", "transform = transforms.Compose([\n", "    transforms.Resize((299, 299)),  # Inception-v3 expects 299x299 input\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])\n", "])\n", "\n", "# Load dataset\n", "dataset = KidneyCTDataset(root_dir=DATASET_PATH, transform=transform)\n", "\n", "# Get all patient IDs\n", "patient_ids = list(dataset.patient_to_samples.keys())\n", "print(f\"Total patients: {len(patient_ids)}\")\n", "print(f\"Total images: {len(dataset)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 1: Perform a clean 80-20 split by patient ID\n", "train_patients, test_patients = train_test_split(\n", "    patient_ids, \n", "    test_size=0.2, \n", "    random_state=42\n", ")\n", "\n", "print(f\"Train-Test Split (by patient):\")\n", "print(f\"  Train: {len(train_patients)} patients ({len(train_patients)/len(patient_ids):.1%} of all patients)\")\n", "print(f\"  Test: {len(test_patients)} patients ({len(test_patients)/len(patient_ids):.1%} of all patients)\")\n", "\n", "# Get sample indices for each split\n", "train_indices = [idx for patient in train_patients for idx in dataset.patient_to_samples[patient]]\n", "test_indices = [idx for patient in test_patients for idx in dataset.patient_to_samples[patient]]\n", "\n", "print(f\"\\nTrain-Test Split (by image):\")\n", "print(f\"  Train: {len(train_indices)} images ({len(train_indices)/len(dataset):.1%} of all images)\")\n", "print(f\"  Test: {len(test_indices)} images ({len(test_indices)/len(dataset):.1%} of all images)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 2: Divide the training patients into 5 completely separate folds\n", "# Shuffle the training patients\n", "random.shuffle(train_patients)\n", "\n", "# Split into 5 equal groups\n", "num_folds = 5\n", "fold_size = len(train_patients) // num_folds\n", "fold_patients = [train_patients[i*fold_size:(i+1)*fold_size] for i in range(num_folds)]\n", "\n", "# Handle any remaining patients\n", "for i in range(len(train_patients) % num_folds):\n", "    fold_patients[i].append(train_patients[num_folds*fold_size + i])\n", "\n", "# Create a list to store fold information\n", "folds = []\n", "\n", "print(\"\\n5-Fold Split (No Data Reuse):\")\n", "for fold_idx in range(num_folds):\n", "    # Get sample indices for this fold\n", "    fold_indices = [idx for patient in fold_patients[fold_idx] for idx in dataset.patient_to_samples[patient]]\n", "    \n", "    # Store fold information\n", "    folds.append({\n", "        'patients': fold_patients[fold_idx],\n", "        'indices': fold_indices\n", "    })\n", "    \n", "    print(f\"Fold {fold_idx+1}: {len(fold_patients[fold_idx])} patients, {len(fold_indices)} images\")\n", "\n", "# Verify no overlap between folds\n", "for i in range(num_folds):\n", "    for j in range(i+1, num_folds):\n", "        overlap = set(folds[i]['indices']).intersection(set(folds[j]['indices']))\n", "        if len(overlap) > 0:\n", "            print(f\"❌ Overlap between fold {i+1} and fold {j+1}: {len(overlap)} images\")\n", "        else:\n", "            print(f\"✓ No overlap between fold {i+1} and fold {j+1}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create test dataset\n", "test_dataset = Subset(dataset, test_indices)\n", "\n", "# Create test data loader (same for all folds)\n", "test_loader = DataLoader(\n", "    test_dataset, \n", "    batch_size=8, \n", "    shuffle=False, \n", "    num_workers=0,\n", "    pin_memory=True\n", ")\n", "\n", "# Create fold datasets\n", "fold_datasets = [Subset(dataset, fold['indices']) for fold in folds]\n", "\n", "# Print fold sizes\n", "print(\"Fold dataset sizes:\")\n", "for i, fold_dataset in enumerate(fold_datasets):\n", "    print(f\"  Fold {i+1}: {len(fold_dataset)} images\")\n", "print(f\"  Test: {len(test_dataset)} images\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function to train a model for one epoch\n", "def train_epoch(model, train_loader, criterion, optimizer, epoch):\n", "    model.train()\n", "    running_loss = 0.0\n", "    correct = 0\n", "    total = 0\n", "    \n", "    # Use tqdm for progress bar\n", "    with tqdm(train_loader, desc=f'Epoch {epoch+1}', leave=False) as t:\n", "        for inputs, labels in t:\n", "            # Skip batches that are too small (to avoid batch norm errors)\n", "            if inputs.size(0) < 2:\n", "                print(f\"Skipping batch with size {inputs.size(0)} to avoid batch norm errors\")\n", "                continue\n", "                \n", "            inputs, labels = inputs.to(device), labels.to(device)\n", "            \n", "            # Zero the parameter gradients\n", "            optimizer.zero_grad()\n", "            \n", "            try:\n", "                # Forward pass\n", "                outputs = model(inputs)\n", "                \n", "                # Handle Inception-v3's special output format when aux_logits=True\n", "                if isinstance(outputs, tuple):\n", "                    # Inception-v3 with aux_logits returns (output, aux_output)\n", "                    main_output = outputs[0]\n", "                    aux_output = outputs[1]\n", "                    \n", "                    # Calculate main loss\n", "                    loss = criterion(main_output, labels)\n", "                    \n", "                    # Add auxiliary loss with weight 0.3\n", "                    loss += 0.3 * criterion(aux_output, labels)\n", "                else:\n", "                    # Normal case for other models or when aux_logits=False\n", "                    main_output = outputs\n", "                    loss = criterion(main_output, labels)\n", "                \n", "                # Backward pass and optimize\n", "                loss.backward()\n", "                optimizer.step()\n", "                \n", "                # Track statistics (using main output for accuracy)\n", "                running_loss += loss.item() * inputs.size(0)\n", "                _, predicted = torch.max(main_output, 1)\n", "                total += labels.size(0)\n", "                correct += (predicted == labels).sum().item()\n", "                \n", "                # Update progress bar\n", "                t.set_postfix(loss=loss.item(), acc=100.*correct/total)\n", "                \n", "            except ValueError as e:\n", "                if \"Expected more than 1 value per channel when training\" in str(e):\n", "                    print(f\"Skipping batch due to batch norm error: {e}\")\n", "                    continue\n", "                else:\n", "                    raise e\n", "    \n", "    epoch_loss = running_loss / len(train_loader.dataset)\n", "    epoch_acc = 100. * correct / total\n", "    \n", "    return epoch_loss, epoch_acc\n", "\n", "# Function to evaluate the model\n", "def evaluate(model, data_loader, criterion):\n", "    model.eval()\n", "    running_loss = 0.0\n", "    correct = 0\n", "    total = 0\n", "    \n", "    with torch.no_grad():\n", "        for inputs, labels in data_loader:\n", "            inputs, labels = inputs.to(device), labels.to(device)\n", "            \n", "            # Forward pass\n", "            outputs = model(inputs)\n", "            \n", "            # Handle Inception-v3's special output format\n", "            if isinstance(outputs, tuple):\n", "                # Use only the main output\n", "                main_output = outputs[0]\n", "                loss = criterion(main_output, labels)\n", "            else:\n", "                main_output = outputs\n", "                loss = criterion(main_output, labels)\n", "            \n", "            # Track statistics\n", "            running_loss += loss.item() * inputs.size(0)\n", "            _, predicted = torch.max(main_output, 1)\n", "            total += labels.size(0)\n", "            correct += (predicted == labels).sum().item()\n", "    \n", "    epoch_loss = running_loss / len(data_loader.dataset)\n", "    epoch_acc = 100. * correct / total\n", "    \n", "    return epoch_loss, epoch_acc"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function to generate predictions and true labels\n", "def get_predictions(model, data_loader, device):\n", "    model.eval()\n", "    all_preds = []\n", "    all_labels = []\n", "    \n", "    with torch.no_grad():\n", "        for inputs, labels in data_loader:\n", "            inputs, labels = inputs.to(device), labels.to(device)\n", "            \n", "            # Forward pass\n", "            outputs = model(inputs)\n", "            \n", "            # Handle Inception-v3's special output format\n", "            if isinstance(outputs, tuple):\n", "                # Use only the main output\n", "                outputs = outputs[0]\n", "                \n", "            _, predicted = torch.max(outputs, 1)\n", "            \n", "            # Collect predictions and labels\n", "            all_preds.extend(predicted.cpu().numpy())\n", "            all_labels.extend(labels.cpu().numpy())\n", "    \n", "    return np.array(all_preds), np.array(all_labels)\n", "\n", "# Function to plot confusion matrix\n", "def plot_confusion_matrix(cm, classes, normalize=False, title='Confusion Matrix', cmap=plt.cm.Blues):\n", "    plt.figure(figsize=(10, 8))\n", "    \n", "    if normalize:\n", "        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]\n", "        fmt = '.2f'\n", "        title = 'Normalized ' + title\n", "    else:\n", "        fmt = 'd'\n", "    \n", "    # Create heatmap\n", "    sns.heatmap(cm, annot=True, fmt=fmt, cmap=cmap, \n", "                xticklabels=classes, yticklabels=classes, \n", "                square=True, cbar=True)\n", "    \n", "    plt.title(title, fontsize=16)\n", "    plt.ylabel('True Label', fontsize=12)\n", "    plt.xlabel('Predicted Label', fontsize=12)\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function to initialize Inception-v3 model\n", "def initialize_inception_v3():\n", "    # Load pre-trained Inception-v3 model with auxiliary outputs enabled\n", "    # Note: aux_logits must be True when using pre-trained weights\n", "    model = models.inception_v3(weights='DEFAULT', aux_logits=True)\n", "    \n", "    # Modify the final fully connected layer for our number of classes\n", "    num_ftrs = model.fc.in_features\n", "    model.fc = nn.Linear(num_ftrs, len(class_labels))\n", "    \n", "    # Also modify the auxiliary classifier layer\n", "    model.AuxLogits.fc = nn.Linear(model.AuxLogits.fc.in_features, len(class_labels))\n", "    \n", "    # Fix batch normalization layers to use eval mode during training\n", "    # This prevents the \"Expected more than 1 value per channel when training\" error\n", "    def fix_bn(m):\n", "        if isinstance(m, nn.BatchNorm2d):\n", "            m.eval()\n", "            # Freeze parameters\n", "            for param in m.parameters():\n", "                param.requires_grad = False\n", "                \n", "    # Apply the fix to all batch norm layers in the auxiliary classifier\n", "    model.AuxLogits.apply(fix_bn)\n", "    \n", "    return model.to(device)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function to train Inception-v3 models on each fold\n", "def train_inception_v3_on_folds(num_epochs=10, batch_size=32):  # Increased batch size to 32 to avoid batch norm issues\n", "    # Results storage\n", "    fold_results = []\n", "    all_train_accs = []\n", "    all_test_accs = []\n", "    \n", "    # Loop through each fold\n", "    for fold_idx in range(num_folds):\n", "        print(f\"\\n=== Training Inception-v3 on Fold {fold_idx+1}/{num_folds} ===\\n\")\n", "        \n", "        # Create data loader for this fold\n", "        train_loader = DataLoader(\n", "            fold_datasets[fold_idx],\n", "            batch_size=batch_size,\n", "            shuffle=True,\n", "            num_workers=0,\n", "            pin_memory=True,\n", "            drop_last=True  # Drop the last incomplete batch to avoid batch norm errors\n", "        )\n", "        \n", "        print(f\"Training on {len(fold_datasets[fold_idx])} images\")\n", "        \n", "        # Initialize Inception-v3 model\n", "        model = initialize_inception_v3()\n", "        \n", "        # Loss function and optimizer\n", "        criterion = nn.CrossEntropyLoss()\n", "        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)\n", "        \n", "        # Training loop\n", "        results = {\n", "            'train_loss': [], 'train_acc': [],\n", "            'test_loss': 0, 'test_acc': 0,\n", "            'final_train_acc': 0\n", "        }\n", "        \n", "        for epoch in range(num_epochs):\n", "            # Train for one epoch\n", "            train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, epoch)\n", "            \n", "            # Print statistics\n", "            print(f'Epoch {epoch+1}/{num_epochs}:')\n", "            print(f'  Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%')\n", "            \n", "            # Save statistics\n", "            results['train_loss'].append(train_loss)\n", "            results['train_acc'].append(train_acc)\n", "            \n", "            # Save the final train accuracy for the last epoch\n", "            if epoch == num_epochs - 1:\n", "                results['final_train_acc'] = train_acc\n", "                all_train_accs.append(train_acc)\n", "        \n", "        # Set aux_logits to False for inference\n", "        model.aux_logits = False\n", "        \n", "        # Save the final model\n", "        model_path = f'inception_v3_fold{fold_idx+1}.pth'\n", "        torch.save(model.state_dict(), model_path)\n", "        print(f'Saved final model to {model_path}')\n", "        \n", "        # Evaluate on test set after training is complete\n", "        test_loss, test_acc = evaluate(model, test_loader, criterion)\n", "        results['test_loss'] = test_loss\n", "        results['test_acc'] = test_acc\n", "        all_test_accs.append(test_acc)\n", "        \n", "        print(f'\\nFold {fold_idx+1} Results:')\n", "        print(f'  Final Train Acc: {results[\"final_train_acc\"]:.2f}%')\n", "        print(f'  Test Loss: {test_loss:.4f}, Test Acc: {test_acc:.2f}%')\n", "        \n", "        # Generate confusion matrix for this fold\n", "        print(f\"\\nGenerating confusion matrix for Fold {fold_idx+1}...\")\n", "        y_pred, y_true = get_predictions(model, test_loader, device)\n", "        cm = confusion_matrix(y_true, y_pred)\n", "        \n", "        print(f\"Confusion Matrix for Fold {fold_idx+1}:\")\n", "        print(cm)\n", "        \n", "        # Generate classification report\n", "        report = classification_report(y_true, y_pred, target_names=class_labels)\n", "        print(f\"\\nClassification Report for Fold {fold_idx+1}:\")\n", "        print(report)\n", "        \n", "        # Plot confusion matrices\n", "        plot_confusion_matrix(cm, class_labels, normalize=False, \n", "                             title=f'Inception-v3 Fold {fold_idx+1} Confusion Matrix')\n", "        plot_confusion_matrix(cm, class_labels, normalize=True, \n", "                             title=f'Inception-v3 Fold {fold_idx+1} Normalized Confusion Matrix')\n", "        \n", "        # Add to fold results\n", "        fold_results.append(results)\n", "    \n", "    # Calculate average results across all folds\n", "    avg_train_acc = np.mean(all_train_accs)\n", "    avg_test_acc = np.mean(all_test_accs)\n", "    \n", "    print('\\n=== Overall Inception-v3 Results ===')\n", "    print(f'Average Train Accuracy: {avg_train_acc:.2f}%')\n", "    print(f'Average Test Accuracy: {avg_test_acc:.2f}%')\n", "    \n", "    # Plot training accuracy\n", "    plt.figure(figsize=(15, 5))\n", "    for i, fold_result in enumerate(fold_results):\n", "        plt.plot(fold_result['train_acc'], '-', label=f'Fold {i+1} Train')\n", "    plt.title(f'Inception-v3 - Training Accuracy', fontsize=14)\n", "    plt.xlabel('Epoch', fontsize=12)\n", "    plt.ylabel('Accuracy (%)', fontsize=12)\n", "    plt.legend()\n", "    plt.grid(True)\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Display final test accuracies\n", "    plt.figure(figsize=(10, 6))\n", "    test_accs = [fold['test_acc'] for fold in fold_results]\n", "    plt.bar(range(1, num_folds+1), test_accs, color='#3498db')\n", "    plt.axhline(y=avg_test_acc, color='r', linestyle='-', label=f'Average: {avg_test_acc:.2f}%')\n", "    plt.title(f'Inception-v3 - Test Accuracy by Fold', fontsize=14)\n", "    plt.xlabel('Fold', fontsize=12)\n", "    plt.ylabel('Test Accuracy (%)', fontsize=12)\n", "    plt.xticks(range(1, num_folds+1))\n", "    plt.legend()\n", "    plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "    \n", "    # Add value labels\n", "    for i, v in enumerate(test_accs):\n", "        plt.text(i+1, v+1, f\"{v:.2f}%\", ha='center', fontsize=10)\n", "        \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Display final train accuracies\n", "    plt.figure(figsize=(10, 6))\n", "    train_accs = [fold['final_train_acc'] for fold in fold_results]\n", "    plt.bar(range(1, num_folds+1), train_accs, color='#2ecc71')\n", "    plt.axhline(y=avg_train_acc, color='r', linestyle='-', label=f'Average: {avg_train_acc:.2f}%')\n", "    plt.title(f'Inception-v3 - Final Train Accuracy by Fold', fontsize=14)\n", "    plt.xlabel('Fold', fontsize=12)\n", "    plt.ylabel('Train Accuracy (%)', fontsize=12)\n", "    plt.xticks(range(1, num_folds+1))\n", "    plt.legend()\n", "    plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "    \n", "    # Add value labels\n", "    for i, v in enumerate(train_accs):\n", "        plt.text(i+1, v+1, f\"{v:.2f}%\", ha='center', fontsize=10)\n", "        \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return fold_results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the training\n", "inception_v3_results = train_inception_v3_on_folds(num_epochs=10, batch_size=32)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 4}