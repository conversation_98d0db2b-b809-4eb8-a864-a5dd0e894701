# Fix for GoogLeNet in final_5fold_cv_modified.ipynb

# Replace the train_epoch function with this:
def train_epoch(model, train_loader, criterion, optimizer, epoch):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    
    # Use tqdm for progress bar
    with tqdm(train_loader, desc=f'Epoch {epoch+1}', leave=False) as t:
        for inputs, labels in t:
            inputs, labels = inputs.to(device), labels.to(device)
            
            # Zero the parameter gradients
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(inputs)
            
            # Handle GoogLeNet's special output format when aux_logits=True
            if isinstance(outputs, tuple):
                # GoogLeNet with aux_logits returns (output, aux1, aux2)
                main_output = outputs[0]
                aux_outputs = outputs[1:]
                
                # Calculate main loss
                loss = criterion(main_output, labels)
                
                # Add auxiliary losses with weight 0.3
                for aux_output in aux_outputs:
                    loss += 0.3 * criterion(aux_output, labels)
            else:
                # Normal case for other models
                main_output = outputs
                loss = criterion(main_output, labels)
            
            # Backward pass and optimize
            loss.backward()
            optimizer.step()
            
            # Track statistics (using main output for accuracy)
            running_loss += loss.item() * inputs.size(0)
            _, predicted = torch.max(main_output, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            
            # Update progress bar
            t.set_postfix(loss=loss.item(), acc=100.*correct/total)
    
    epoch_loss = running_loss / len(train_loader.dataset)
    epoch_acc = 100. * correct / total
    
    return epoch_loss, epoch_acc

# Replace the evaluate function with this:
def evaluate(model, data_loader, criterion):
    model.eval()
    running_loss = 0.0
    correct = 0
    total = 0
    
    with torch.no_grad():
        for inputs, labels in data_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            
            # Forward pass
            outputs = model(inputs)
            
            # Handle GoogLeNet's special output format
            if isinstance(outputs, tuple):
                # Use only the main output
                main_output = outputs[0]
                loss = criterion(main_output, labels)
            else:
                main_output = outputs
                loss = criterion(main_output, labels)
            
            # Track statistics
            running_loss += loss.item() * inputs.size(0)
            _, predicted = torch.max(main_output, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
    
    epoch_loss = running_loss / len(data_loader.dataset)
    epoch_acc = 100. * correct / total
    
    return epoch_loss, epoch_acc

# Replace the get_predictions function with this:
def get_predictions(model, data_loader, device):
    model.eval()
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for inputs, labels in data_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            
            # Forward pass
            outputs = model(inputs)
            
            # Handle GoogLeNet's special output format
            if isinstance(outputs, tuple):
                # Use only the main output
                outputs = outputs[0]
                
            _, predicted = torch.max(outputs, 1)
            
            # Collect predictions and labels
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    return np.array(all_preds), np.array(all_labels)
