{"name": "@jupyterlab/galata-extension", "version": "5.3.5", "private": true, "description": "JupyterLab UI Testing Framework Extension.", "keywords": ["jup<PERSON><PERSON>", "jupyterlab", "jupyterlab-extension"], "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "main": "../lib/extension/index.js", "types": "../lib/extension/index.d.ts", "files": ["../lib/extension/**/*.{d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}"], "scripts": {"build": "npm run build:lib && npm run build:labextension", "build:labextension": "node ../../node_modules/@jupyterlab/builder/lib/build-labextension.js --core-path ../../dev_mode --development .", "build:lib": "tsc", "clean": "npm run clean:lib && npm run clean:labextension", "clean:labextension": "rimraf ../../jupyterlab/galata/@jupyterlab", "clean:lib": "rimraf ../lib/extension tsconfig.tsbuildinfo"}, "dependencies": {"@jupyterlab/application": "^4.3.5", "@jupyterlab/apputils": "^4.4.5", "@jupyterlab/cells": "^4.3.5", "@jupyterlab/debugger": "^4.3.5", "@jupyterlab/docmanager": "^4.3.5", "@jupyterlab/nbformat": "^4.3.5", "@jupyterlab/notebook": "^4.3.5", "@jupyterlab/settingregistry": "^4.3.5", "@lumino/algorithm": "^2.0.2", "@lumino/coreutils": "^2.2.0", "@lumino/signaling": "^2.1.3"}, "devDependencies": {"@jupyterlab/builder": "^4.3.5", "rimraf": "~5.0.5", "typescript": "~5.1.6"}, "jupyterlab": {"extension": true, "outputDir": "../../jupyterlab/galata/@jupyterlab/galata-extension", "_build": {"load": "static/remoteEntry.bccd23f6b01988a7e8b7.js", "extension": "./extension"}}}